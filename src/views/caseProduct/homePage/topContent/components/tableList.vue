<template>
  <el-config-provider :locale="zhCn">
    <div class="">
      <div class="searchInfo flex" :class="switchOnOff ? 'AIBg' : 'commonBg'">
        <div class="vocationPull" style="flex: 1; height: 56px">
          <!-- <div class="switch">
          <div class="AIlogo"></div>
          <a-switch
            checked-children="on"
            un-checked-children="off"
            v-model:checked="switchOnOff"
          />
        </div> -->
          <a-config-provider
            :locale="zhCN"
            :getPopupContainer="(triggerNode) => triggerNode.parentNode"
          >
            <div class="line"></div>

            <div class="lines"></div>
            <a-input
              v-model:value="name"
              class="inputClass"
              allow-clear
              height="56px"
              @keyup.enter="seekContent"
              placeholder="请输入案例名称、标签等关键词进行搜索"
            />
            <div
              class="seekInfo"
              :class="switchOnOff ? 'AIbtn' : 'commonBtn'"
              @click="seekContent"
            >
              <img src="@/assets/images/home/<USER>" alt="" />
              <div>搜索</div>
            </div>
          </a-config-provider>
        </div>
      </div>
      <div class="newLoading" v-if="AIsearch">
        <loadingSmall />
      </div>

      <div class="selectData" ref="selectData" v-if="true || !switchOnOff">
        <div
          :class="[
            'selcet_box',
            { showMore: showScense == 'label' && showIndex === index },
          ]"
          v-for="(val, index) in vocationList"
          :key="index"
        >
          <div class="left_select">{{ val.label }}</div>
        <div :class="[
          'right_select',
          { showHidden: showScense == 'label' && showIndex === index },
        ]" :style="{
          'background-color': val.level === 2 ? '#F3F8FF' : (val.level === 3 ? '#DDEAFF' : '#FFFFFFFF'),
        }">
          <span v-for="(value, key1) in val.children" :key="key1"
            :class="{ activeBtn: providerSelect.includes(value.label) }" style="height: 49px">
            <div class="title" @click="providerBtn(value, 'default', index)" :style="{
              'background-color': providerSelect.includes(value.label) ? '#F3F8FF' : '#FFFFFFFF',
            }">
              {{ value.label }}
            </div>
          </span>
        </div>
          <span
            class="more flex"
            v-if="
              val.children && val.children.length > 8 && showIndex !== index
            "
            @click="showMore('label', index)"
            >更多<img src="@/assets/images/solution/home/<USER>" alt=""
          /></span>
          <span
            class="more flex"
            v-if="
              val.children && val.children.length > 8 && showIndex === index
            "
            @click="showless('label_less', index)"
            >收起<img src="@/assets/images/solution/home/<USER>" alt=""
          /></span>
        </div>
        <div
          v-if="vocationList[0]"
          v-for="(value, key1) in vocationList[0].children"
          @mouseleave="providerLeave(value, key1)"
          :key="key1"
        >
          <div
            class="last_data_top"
            :style="getBoxTitle(key1)"
            v-if="showLast && showId == value.value"
            @click="providerBtn(value, 'default', key1)"
          >
            {{ value.label }}
          </div>
          <div
            class="last_data"
            v-if="showLast && showId == value.value"
            :style="getBoxLeft(key1)"
          >
            <!--{ left: -135 * key1 + 'px' }-->
            <span
              v-for="(e, i) in value.children"
              @click="providerBtn(e, 'last', i, value)"
              style="width: auto; padding: 11px 20px; cursor: pointer"
              :class="{ activeBtn: providerSelect.indexOf(e.label) > -1 }"
              :key="i"
            >
              {{ e.label }}
            </span>
          </div>
        </div>
        <div class="selcet_box">
          <div class="left_select">筛选</div>
          <div class="right_select expert_select">
            <div class="flex">
              <el-select
                v-model="cityTags"
                placeholder="请选择省/市"
                style="width: 240px; height: 32px"
                @change="cityChange"
              >
                <el-option
                  v-for="item in cityList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
              <div class="margin_l_24">
                <div class="amountPrice">
                  <a-input-number
                    v-model:value="minPriceAmount"
                    placeholder="请输入最低价"
                    style="width: 148px; border: none"
                    :precision="2"
                    :min="0"
                    @blur="minBlur"
                    @change="minInput"
                  />
                  <span class="separator">~</span>
                  <a-input-number
                    v-model:value="maxPriceAmount"
                    placeholder="请输入最高价"
                    :precision="2"
                    :min="0"
                    @change="maxInput"
                    @blur="maxBlur"
                    style="width: 148px; border: none"
                  />
                  <span style="color: rgba(0, 0, 0, 0.65); margin-right: 12px">
                    万元
                  </span>
                </div>
              </div>
              <div class="margin_l_24">
                <el-date-picker
                  v-model="timeRange"
                  type="monthrange"
                  range-separator="-"
                  start-placeholder="请选择开始年月"
                  end-placeholder="请选择结束年月"
                  @change="timeChange"
                  value-format="YYYY-MM"
                  format="YYYY-MM"
                >
                </el-date-picker>
              </div>
            </div>
          </div>
        </div>

        <div v-if="!showLabel" class="second_line">
          <span @click="toggleShowLabel">收起</span>
          <div class="img_box">
            <img
              src="@/assets/images/solution/home/<USER>"
              class="first"
              alt=""
            />
            <img
              src="@/assets/images/solution/home/<USER>"
              class="sec"
              alt=""
            />
          </div>
        </div>
        <div class="select_boot flex">
          <div>
            已选条件：
            <span v-if="providerSelect.length > 0">产品类型：</span>
            <span v-for="(val, index) in providerSelect" :key="index">
              <span style="margin-left: 8px">
                {{ val }}
              </span>
              <img
                src="@/assets/images/solution/home/<USER>"
                alt=""
                style="width: 16px; height: 16px; cursor: pointer"
                @click="deleteSelect(val, index, 'pro')"
              />
            </span>
            <span v-if="cityTags">所属地市：{{ cityTags }}</span>
            <img
              v-if="cityTags"
              src="@/assets/images/solution/home/<USER>"
              alt=""
              style="width: 16px; height: 16px; cursor: pointer"
              @click="deleteCitySelect"
            />
            <span v-if="timeRange && timeRange.length > 0">
              时间：{{ timeRange[0] }} - {{ timeRange[1] }}
            </span>
            <img
              v-if="timeRange && timeRange.length > 0"
              src="@/assets/images/solution/home/<USER>"
              alt=""
              style="width: 16px; height: 16px; cursor: pointer"
              @click="deleteTimeSelect"
            />
          </div>
          <div class="right_con">
            共找到 <span>{{ totalItemCount }}</span> 条结果
          </div>
        </div>
      </div>

      <div class="tabContent">
        <div v-if="tableList && tableList.length > 0" style="width: 100%">
          <div class="AITips flex align-center" v-if="!showPagination">
            <img
              style="width: 40px; height: 40px; margin-right: 10px"
              src="@/assets/images/AI/ai.png"
              alt=""
            />
            <div class="words">以下是AI助手为您找到的相关结果</div>
          </div>
          <div class="cardContent">
            <div class="card_total flex-1">
              <template v-for="(item, index) in tableList" :key="index">
                <div
                  :class="[
                    'card_content',
                    {
                      cardActive: cardActive == index,
                      rightActive: index % 2 != 0,
                      cardObvious: index < 2 && tableList.length < 3,
                      bottomLine:
                        (index == tableList.length - 1 ||
                          index == tableList.length - 2) &&
                        index > 1,
                    },
                  ]"
                  @mouseenter="contentColor(index)"
                  @mouseleave="contentLeave"
                  @click="proDetail(item)"
                >
                  <div style="display: flex; margin: 24px">
                    <div>
                      <img
                        v-if="item.image"
                        v-lazy="`${item.image}`"
                        style="width: 168px; height: 105px"
                      />
                      <div
                        v-else
                        style="
                          width: 168px;
                          height: 105px;
                          text-align: center;
                          position: relative;
                        "
                        :style="backgroundStyles()"
                      >
                        <p
                          style="
                            font-size: 7px;
                            font-weight: 700;
                            display: block;
                            color: #0a7aee;
                            position: absolute;
                            left: 50%;
                            top: 50%;
                            transform: translate(-50%, -50%);
                          "
                        >
                          {{ item.productCaseName }}
                        </p>
                      </div>
                    </div>
                    <div class="card_center">
                      <div class="card_text">
                        <div class="card_tag">
                          <div class="card_title">
                            {{ item.productCaseName }}
                          </div>
                        </div>
                      </div>
                      <div class="card_des">
                        {{ item.caseSummary }}
                      </div>
                      <div
                        style="
                          display: flex;
                          align-items: center;
                          justify-content: space-between;
                        "
                      >
                        <div
                          style="
                            display: flex;
                            align-items: center;
                            justify-content: space-between;
                            margin-right: 5px;
                          "
                        ></div>
                      </div>
                    </div>
                  </div>
                </div>
              </template>
            </div>
          </div>
          <div class="layPage">
            <a-pagination
              v-model:pageSize="pageItemSize"
              v-model:current="currentPage"
              :pageSizeOptions="pageSizeOptions"
              show-quick-jumper
              show-size-changer
              :total="totalItemCount"
              @change="pageChange"
              @showSizeChange="sizeChange"
              class="mypage"
            />
          </div>
        </div>
        <div v-if="tableList.length == 0" class="emptyPhoto">
          <img src="@/assets/images/home/<USER>" alt="" />
        </div>
        <div class="loading" v-show="loadingShow">
          <a-spin />
        </div>
      </div>
    </div>
  </el-config-provider>
</template>

<script>
import { defineComponent, reactive, toRefs, ref, watch } from "vue";
import { useRouter } from "vue-router";
import { useHomeStore } from "@/store";
import bac from "@/assets/images/noDataBac.png";
import { addShoppingCart } from "@/api/combine/shoppingCart.js";
import voiceRecorder from "@/components/voiceRecorder/voiceRecorder.vue";
import zhCN from "ant-design-vue/es/locale/zh_CN";
import zhCn from "element-plus/es/locale/lang/zh-cn";
import { getMakeUrl } from "@/utils/getUrl";
import eventBus from "@/utils/eventBus";
import { caseProductList } from "@/api/caseProduct/index";
import { getLabelList } from "@/api/dispatchCenter/label";
import { AISearch, AIvoice } from "@/api/AI/ai.js";
import loadingSmall from "@/components/superLoadingSmall/loadingSmall.vue";
import { message } from "ant-design-vue";

export default defineComponent({
  components: {
    voiceRecorder,
    loadingSmall,
  },
  props: {
    reGetList: {
      type: Boolean,
      default: false,
    },
  },
  setup(props) {
    const baseURL = getMakeUrl();
    const vocation = ref("");
    const box = ref(null);
    const selectData = ref(null);
    const data = reactive({
      name: "",
      moment: "",
      backgroundImage: bac,
      loadingShow: true,
      showLabel: true,
      AIsearch: false,
      activeKey: "",
      cardActive: "-1",
      pageSizeOptions: ["10", "20", "30", "50"],
      totalItemCount: 0,
      tabList: [],
      vocationList: [
        { label: "产品类型", value: 1, type: 1, length: 0, children: [] },
      ],
      cityList: [
        { label: "江苏省", value: "江苏省" },
        { label: "南京市", value: "南京市" },
        { label: "苏州市", value: "苏州市" },
        { label: "无锡市", value: "无锡市" },
        { label: "常州市", value: "常州市" },
        { label: "镇江市", value: "镇江市" },
        { label: "南通市", value: "南通市" },
        { label: "扬州市", value: "扬州市" },
        { label: "泰州市", value: "泰州市" },
        { label: "盐城市", value: "盐城市" },
        { label: "淮安市", value: "淮安市" },
        { label: "宿迁市", value: "宿迁市" },
        { label: "徐州市", value: "徐州市" },
        { label: "连云港市", value: "连云港市" },
      ],
      cityTags: "",
      tableList: [],
      totalNum: 0,
      totalItemCount1: 0,
      currentPage: 1,
      pageItemSize: 10,
      labelIdlist: [],
      productLabelId: "",
      showLast: false,
      showId: undefined,
      selectList: [],
      selectListNew: [],
      selectListOld: [],
      showMore: false,
      showScense: "",
      morePro: true,
      providerSelect: [],
      showIndex: "",
      minPriceAmount: null,
      maxPriceAmount: null,
      showPagination: true,
      switchOnOff: false,
      isTranslating: false,
      canBtnUse: false,
      AISearchType: 7,
      tableAIAllList: [],
      filterArr: [],
      timeRange: [],
      newContentName: "手动定制",
    });
    watch(
      () => props.reGetList,
      (val) => {
        if (val) {
          data.activeKey = "";
          data.showLast = false;
          data.showId = "";
          data.name = "";
          data.labelIdlist = [];
          data.providerSelect = [];
          data.selectList = [];
          data.selectListNew = [];
          data.selectListOld = [];
          data.currentPage = "1";
          data.pageItemSize = "10";
          getList();
        }
      }
    );

    const getList = () => {
      let pageParams = {
        pageNo: data.currentPage,
        pageSize: data.pageItemSize,
        productCaseName: data.name,
        productLabelId: data.productLabelId,
        cityTags: data.cityTags,
        minPriceAmount: data.minPriceAmount,
        maxPriceAmount: data.maxPriceAmount,
        minMonth:
          data.timeRange && data.timeRange.length > 0
            ? data.timeRange[0]
            : null,
        maxMonth:
          data.timeRange && data.timeRange.length > 0
            ? data.timeRange[1]
            : null,
      };
      data.loadingShow = true;
      caseProductList(pageParams)
        .then((res) => {
          data.showPagination = true;
          data.loadingShow = false;
          data.tableList = [];
          data.tableList = res.data.rows;
          data.totalItemCount = res.data.totalRows;
          data.tableList.map((item) => {
            item.image = item.image.split(",")[0];
            item.description = item.summary;
            item.logo = item.image;
          });

          if (data.activeKey == "") {
            data.totalItemCount1 = res.data.totalRows;
          }
        })
        .catch(() => {
          data.loadingShow = false;
        });
    };
    getList();
    const providerList = () => {
      let params = {
        pageNo: 1,
        pageSize: 50,
        type: 4,
      };
      getLabelList(params).then((res) => {
        data.vocationList[0].length = res.data.rows.length;
        data.vocationList[0].children = res.data.rows.map((item) => ({
          label: item.name,
          value: item.id,
          length: item.children ? item.children.length : 0,
          // children: item.children
          //   ? item.children.map((child) => ({
          //       label: child.name,
          //       value: child.id,
          //       children: child.children
          //         ? child.children.map((ele) => ({
          //             label: ele.name,
          //             value: ele.id,
          //           }))
          //         : undefined,
          //     }))
          //   : undefined,
        }));
      });
    };
    providerList();
    const seekContent = () => {
      data.currentPage = 1;
      if (data.switchOnOff) {
        getAIList();
      } else {
        getList();
      }
    };

    const add = (id) => {
      let addParams = {
        schemeId: id,
        type: "1",
      };
      if (data.sourceType == "1") {
        addParams.type = "1";
      } else {
        addParams.type = "3";
      }
      addShoppingCart(addParams).then(() => {
        if (data.showPagination) {
          getList();
        } else {
          getAIList();
        }
        eventBus.emit("cartRefresh");
      });
    };
    const tabChange = (val) => {
      if (val.value !== data.activeKey) {
        data.activeKey = vocation.value = val.value;
        data.currentPage = 1;
        getList();
      }
    };
    const contentColor = (index) => {
      data.cardActive = index;
    };
    const router = useRouter();
    const proDetail = (val) => {
      if (data.sourceType == "1") {
        router.push({
          query: {
            id: val.id,
          },
          name: "caseProductDetail",
        });
      } else {
        router.push({
          query: {
            id: val.id,
            activeBtn: 2,
          },
          name: "caseProductDetail",
        });
      }
    };
    const contentLeave = () => {
      data.cardActive = "-1";
    };
    const getAIPageList = () => {
      let firstPageNum = (data.currentPage - 1) * data.pageItemSize;
      let lastPageNum = data.currentPage * data.pageItemSize;
      data.tableList = data.tableAIAllList.slice(firstPageNum, lastPageNum);
      data.totalItemCount = data.tableAIAllList.length;
    };
    const pageChange = (page, _pageSize) => {
      data.currentPage = page;
      if (data.showPagination) {
        getList();
      } else {
        getAIPageList();
      }
    };
    const sizeChange = (_current, size) => {
      data.pageItemSize = size;
      if (data.showPagination) {
        getList();
      } else {
        getAIPageList();
      }
    };
    const labelChange = (val) => {
      data.labelIdlist = val.join(",");
    };
    const labelSelect = (value, index, type = "default") => {
      if (value.children && type !== "last") {
        if (data.selectList.includes(value.label)) {
          data.showLast = false;
        } else {
          data.showLast = true;
        }
        data.showId = value.value;
        data.showScense = "label";
        data.showIndex = index;
      }
      if (data.selectList.includes(value.label)) {
        const index = data.selectList.findIndex((item) => item === value.label);
        if (index !== -1) {
          data.selectList.splice(index, 1);
        }
        const index1 = data.selectListNew.findIndex(
          (item) => item === value.value
        );
        if (index1 !== -1) {
          data.selectListNew.splice(index, 1);
        }
      } else {
        data.selectList.push(value.label);
        data.selectListNew.push(value.value);
      }
      data.selectList = data.selectList.filter((value, index, self) => {
        return self.indexOf(value) === index;
      });
      data.selectListNew = data.selectListNew.filter((value, index, self) => {
        return self.indexOf(value) === index;
      });
      data.labelIdlist = data.selectListNew.join(",");
      getList();
      //    }
    };
    const providerBtn = (value, type = "default", index, parvalue) => {
      if (value.children && type !== "last") {
        data.showId = value.value;
        data.showScense = "label";
      }
      if (type != "last") {
        data.activeKey = vocation.value = value.value;
        data.productLabelId = value.value;
        if (data.providerSelect.includes(value.label)) {
          data.providerSelect = [];
          data.selectListOld = [];
          data.activeKey = "";
          data.productLabelId = "";
          data.showLast = false;
        } else {
          if (value.children) {
            data.showLast = true;
          } else {
            data.showLast = false;
          }
          data.providerSelect = [];
          data.selectListOld = [];
          data.providerSelect.push(value.label);
          data.selectListOld.push(value.value);
          data.providerSelect = data.providerSelect.filter(
            (value, index, self) => {
              return self.indexOf(value) === index;
            }
          );
          data.selectListOld = data.selectListOld.filter(
            (value, index, self) => {
              return self.indexOf(value) === index;
            }
          );
        }
      } else {
        data.activeKey = parvalue.value;
        if (data.providerSelect.includes(value.label)) {
          data.providerSelect = [parvalue.label];
          data.selectListOld = [parvalue.value];
        } else {
          data.providerSelect = [parvalue.label];
          data.providerSelect.push(value.label);
          data.providerSelect = data.providerSelect.filter(
            (value, index, self) => {
              return self.indexOf(value) === index;
            }
          );
          data.selectListOld = [parvalue.value];
          data.selectListOld.push(value.value);
          data.selectListOld = data.selectListOld.filter(
            (value, index, self) => {
              return self.indexOf(value) === index;
            }
          );
        }
      }
      data.labelIdlist = data.selectListNew.join(",");
      data.currentPage = 1;
      // getList();
      if (data.switchOnOff) {
        if (data.name != "") {
          let industryId =
            data.selectListOld.length == 2
              ? data.selectListOld[1]
              : data.selectListOld.length == 1
              ? data.selectListOld[0]
              : "";
          data.filterArr = data.tableAIAllList.filter((item) => {
            return item.industryIdReq.includes(industryId);
          });
          let firstPageNum = (data.currentPage - 1) * data.pageItemSize;
          let lastPageNum = data.currentPage * data.pageItemSize;
          data.tableList = data.filterArr.slice(firstPageNum, lastPageNum);
          data.totalItemCount = data.filterArr.length;
        } else {
          getList();
        }
      } else {
        getList();
      }
    };

    const cityChange = () => {
      getList();
    };

    const providerEnter = (val, index) => {
      data.showId = val.value;
      data.showLast = true;
    };

    const providerLeave = (val, index) => {
      data.showId = "";
      data.showLast = false;
    };

    const deleteSelect = (_val, index, type) => {
      if (type == "pro") {
        if (index == 1) {
          data.providerSelect = [data.providerSelect[0]];
          data.selectListOld = [data.selectListOld[0]];
        }
        if (index == 0) {
          data.providerSelect = [];
          data.selectListOld = [];
          data.activeKey = "";
          data.productLabelId = "";
          data.showLast = false;
        }
      }
      data.selectList.splice(index, 1);
      data.selectListNew.splice(index, 1);
      data.labelIdlist = data.selectListNew.join(",");
      if (data.switchOnOff) {
        if (data.name != "") {
          let industryId =
            data.selectListOld.length == 2
              ? data.selectListOld[1]
              : data.selectListOld.length == 1
              ? data.selectListOld[0]
              : "";
          data.filterArr = data.tableAIAllList.filter((item) => {
            return item.industryIdReq.includes(industryId);
          });
          let firstPageNum = (data.currentPage - 1) * data.pageItemSize;
          let lastPageNum = data.currentPage * data.pageItemSize;
          data.tableList = data.filterArr.slice(firstPageNum, lastPageNum);
          data.totalItemCount = data.filterArr.length;
        } else {
          getList();
        }
      } else {
        getList();
      }
    };
    const deleteCitySelect = () => {
      data.cityTags = "";
      getList();
    };

    const validateAndSearch = () => {
      const min = data.minPriceAmount;
      const max = data.maxPriceAmount;
      if (min !== undefined && min !== null && max !== undefined && max !== null){
        getList()
      }
      if (min == null && max == null){
        getList()
      }
    };

    const maxBlur = () => {
      validateAndSearch();
    };
    const minBlur = () => {
      validateAndSearch();
    };

    const minInput = (value) => {
      if(data.maxPriceAmount && value >= data.maxPriceAmount) data.minPriceAmount = null
    };
    const maxInput = (value) => {
      if(value == 0) data.maxPriceAmount = null
      if(data.minPriceAmount && value <= data.minPriceAmount) data.maxPriceAmount = null
    };

    const showMore = (type, index) => {
      if (type == "provider") {
        data.showScense = type;
        data.morePro = false;
      } else {
        data.showIndex = index;
        data.showScense = type;
      }
    };
    const showless = (type, _index) => {
      if (type == "provider_less") {
        data.showScense = type;
        data.morePro = true;
      } else {
        data.showIndex = "";
        data.showScense = type;
      }
    };
    const getAIList = () => {
      if (data.name == "") {
        data.showPagination = true;
        getList();
        return false;
      }

      data.activeKey = "";
      data.showId = "";
      data.showScense = "";
      data.showIndex = "";
      data.showLast = false;
      data.labelIdlist = [];
      data.providerSelect = [];
      data.selectList = [];
      data.selectListNew = [];
      data.selectListOld = [];

      data.loadingShow = true;
      data.AIsearch = true;
      AISearch({
        question: data.name,
        type: 7,
      }).then((res) => {
        data.loadingShow = false;
        data.AIsearch = false;
        if (res.code == 200) {
          data.showPagination = false;
          data.tableAIAllList = [];
          data.tableAIAllList = res.data;
          getAIPageList();
          data.totalItemCount = res.data ? res.data.length : 0;
          data.tableList.map((item) => {
            item.image = item.image.split(",")[0];
            if (item.labelName) {
              item.labelName = item.labelName.split(",");
            }
            if (item.provider) {
              item.provider = item.provider.split("/")[1];
            }
            item.description =
              data.sourceType == "2" ? item.summary : item.description;
          });
        }
      });
    };
    const refreshList = () => {
      if (!data.switchOnOff) {
        getList();
      } else {
        getAIList();
      }
    };
    eventBus.on("solutionAllRefresh", refreshList);
    const toggleShowLabel = () => {
      data.showLabel = !data.showLabel;
    };
    const deleteTimeSelect = () => {
      data.timeRange = [];
      getList();
    };
    // 语音输入
    const handleAudio = (audioBlob) => {
      const formData = new FormData();
      formData.append("file", audioBlob, "recording.wav"); // 上传文件
      // 调用 AIvoice 函数并传递音频数据
      data.isTranslating = true;
      data.canBtnUse = true;
      AIvoice(formData).then((res) => {
        data.isTranslating = false;
        data.canBtnUse = false;
        if (res.code == 200) {
          data.name = res.msg;
          // seekContent()
        }
      });
    };
    const backgroundStyles = () => {
      return {
        backgroundImage: `url(${data.backgroundImage})`, // 使用模板字符串来插入变量
        backgroundRepeat: "no-repeat",
        backgroundSize: "cover",
      };
    };

    const timeChange = () => {
      getList();
    };

    const getBoxTitle = (key) => {
      const rect = box.value[key].getBoundingClientRect();
      const selectHtml = selectData.value.getBoundingClientRect();
      return {
        left: rect.x - selectHtml.x + "px",
        top: rect.y - selectHtml.y + "px",
      };
    };

    const getBoxLeft = (key) => {
      const rect = box.value[key].getBoundingClientRect();
      const firstRect = box.value[0].getBoundingClientRect();
      const selectHtml = selectData.value.getBoundingClientRect();
      return {
        left: firstRect.x - selectHtml.x + "px",
        top: rect.y - selectHtml.y + 49 + "px",
      };
    };

    const counterStore = useHomeStore();
    const changeContent = () => {
      if (data.newContentName == "手动定制") {
        data.newContentName = "方案查询";
      } else {
        data.newContentName = "手动定制";
      }
    };
    return {
      ...toRefs(data),
      box,
      selectData,
      vocation,
      labelSelect,
      backgroundStyles,
      getBoxLeft,
      getBoxTitle,
      counterStore,
      toggleShowLabel,
      providerBtn,
      showMore,
      showless,
      deleteSelect,
      deleteCitySelect,
      tabChange,
      contentColor,
      sizeChange,
      contentLeave,
      add,
      maxBlur,
      minBlur,
      minInput,
      maxInput,
      proDetail,
      router,
      pageChange,
      zhCN,
      zhCn,
      timeChange,
      seekContent,
      deleteTimeSelect,
      baseURL,
      cityChange,
      labelChange,
      getAIList,
      refreshList,
      handleAudio,
      changeContent,
      providerEnter,
      providerLeave,
    };
  },
});
</script>

<style lang="scss" scoped src="./tableList.scss"></style>

<style lang="scss">
.ant-select:not(.ant-select-customize-input) .ant-select-selector {
  border: none;
}

::v-deep(.ant-cascader-input.ant-input) {
  border: none !important;
}

.mypage {
  .ant-pagination {
    color: rgba(0, 0, 0, 0.65);
  }

  .ant-select-selector {
    color: rgba(0, 0, 0, 0.65);
  }

  .ant-pagination-item-active {
    background: #007eff;
  }

  .ant-pagination-item-active a {
    color: #ffffff;
  }

  .ant-pagination-item-active:focus a,
  .ant-pagination-item-active:hover a {
    color: #ffffff;
  }

  .ant-pagination-item-active a:focus,
  .ant-pagination-item-active a:hover {
    color: #ffffff;
  }

  .ant-pagination-item:focus,
  .ant-pagination-item:hover {
    background: #007eff;
  }

  .ant-pagination-item:focus a,
  .ant-pagination-item:hover a {
    color: #ffffff;
  }

  .ant-pagination-item-disabled:hover,
  .ant-pagination-item-disabled:focus {
    background-color: #ffffff;
    border-color: #d9d9d9;

    a {
      color: rgba(0, 0, 0, 0.25);
    }
  }

  .ant-pagination-options-size-changer.ant-select {
    margin-right: 0;
  }

  .ant-pagination-options-quick-jumper {
    margin-left: 8px;
  }
}

.AISearchLogo {
  width: 190px;
  height: 80px;

  img {
    width: 100%;
    height: 100%;
  }
}
</style>
