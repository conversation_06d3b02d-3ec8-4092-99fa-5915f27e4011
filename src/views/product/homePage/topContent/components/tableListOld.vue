<template>
  <div class="searchInfo flex" :class="switchOnOff ? 'AIBg' : 'commonBg'">
    <div style="
        min-height: 56px;
        background: #ffffff;
        justify-content: center;
        align-items: center;
      " class="flex"></div>

    <div class="vocationPull" style="flex: 1; height: 56px">
      <div class="switch">
        <div class="AIlogo"></div>
        <a-switch checked-children="on" un-checked-children="off" v-model:checked="switchOnOff" />
      </div>
      <a-config-provider :locale="zhCN" :getPopupContainer="(triggerNode) => triggerNode.parentNode">
        <a-input v-model:value="name" class="inputClass" allow-clear @keyup.enter="seekContent"
          placeholder="请输入产品名称、标签等关键词进行搜索" />

        <div class="seekInfo" :class="switchOnOff ? 'AIbtn' : 'commonBtn'" @click="seekContent">
          <img src="@/assets/images/home/<USER>" />
          <div>搜索</div>
        </div>
      </a-config-provider>
    </div>
  </div>
  <div class="newLoading" v-if="AIsearch">
    <loadingSmall />
  </div>
  <div class="selectData" ref="selectData" v-if="!switchOnOff">
    <div :class="['selcet_box', { showMore: showScense == 'productType' }]">
      <div class="left_select">产品类型：</div>
      <div class="right_select" id="getWidth">
        <span v-for="(item, key) in productTypeList" :class="{ activeBtn: typeSelect == item.label }" :key="key"
          @click="providerBtn(item, 'type')">
          {{ item.label }}
        </span>
      </div>
      <span class="more flex" v-if="productTypeList.length > 8 && morePro" @click="showMore('productType')">更多 <img
          src="@/assets/images/solution/home/<USER>" alt="" /></span>
      <span class="more flex" v-if="productTypeList.length > 8 && !morePro" @click="showLess('provider_less')">收起 <img
          src="@/assets/images/solution/home/<USER>" alt="" /></span>
    </div>
    <div :class="['selcet_box', { showMore: showScense == 'productLabe' }]">
      <div class="left_select">产品标签：</div>
      <div class="right_select" id="getWidth">
        <span ref="box" v-for="(item, key) in productLabelList" :class="{ activeBtn: labeSelect == item.label }" :key="key"
          @click="providerBtn(item, 'label')">
          {{ item.label }}
        </span>
      </div>
      <span class="more flex" v-if="productLabelList.length > 8 && labelPro" @click="showMore('productLabe')">更多 <img
          src="@/assets/images/solution/home/<USER>" alt="" /></span>
      <span class="more flex" v-if="productLabelList.length > 8 && !labelPro" @click="showLess('productLabe_less')">收起
        <img src="@/assets/images/solution/home/<USER>" alt="" /></span>
    </div>
    
    <div v-for="(value, key1) in productClassList" @mouseleave="providerLeave(value,key1)">
      <div class="last_data_top"  :style="getBoxTitle(key1)" v-if="showLast && showId == value.value" @click="providerBtn(value, 'default', key1)">{{ value.label }}</div>
      <div class="last_data" v-if="showLast && showId == value.value" :style="getBoxLeft(key1)">
         <!--{ left: -135 * key1 + 'px' }-->
        <span v-for="(e, i) in value.children" @click="providerBtn(e, 'last', i , value)" style="width: auto;padding: 11px 20px;cursor: pointer;"
          :class="{ activeBtn: providerSelect.indexOf(e.label) > -1 }" :key="i">
          {{ e.label }}
        </span>
     	</div>
    </div>
    
    <div class="select_boot flex">
      <div>
        已选条件：
        <span class="label" v-if="classSelect !== ''">产品分类：</span>
        <span>{{ classSelect }}</span><img src="@/assets/images/solution/home/<USER>" alt=""
          v-if="classSelect !== ''" style="width: 16px; height: 16px; cursor: pointer" @click="deleteSelect('class')" />
        <span class="label" v-if="typeSelect !== ''">产品类型：</span>
        <span>{{ typeSelect }}</span><img src="@/assets/images/solution/home/<USER>" v-if="typeSelect !== ''"
          style="width: 16px; height: 16px; cursor: pointer" @click="deleteSelect('type')" />
        <span class="label" v-if="labeSelect !== ''">产品标签：</span>
        <span>
          <span style="margin-left: 8px">
            {{ labeSelect }}
          </span>
          <img src="@/assets/images/solution/home/<USER>" alt="" v-if="labeSelect !== ''"
            style="width: 16px; height: 16px; cursor: pointer" @click="deleteSelect('label')" />
        </span>
      </div>
      <div class="right_con">
        共找到 <span>{{ totalItemCount }}</span> 条结果
      </div>
    </div>
  </div>
  <div class="tabContent">
    <div v-if="tableList && tableList.length > 0" style="width: 100%">
      <div class="AITips flex align-center" v-if="!showPagination">
        <img style="width: 40px; height: 40px; margin-right: 10px" src="../../../../../assets/images/AI/ai.png"
          alt="" />
        <div class="words">以下是AI助手为您找到的相关结果</div>
      </div>
      <div class="cardContent">
        <div class="card_total flex-1">
          <template v-for="(item, index) in tableList" :key="index">
            <div :class="[
              'card_content',
              {
                cardActive: cardActive == index,
                rightActive: index % 2 != 0,
                cardObvious: index < 2 && tableList.length < 3,
                bottomLine:
                  (index == tableList.length - 1 ||
                    index == tableList.length - 2) &&
                  index > 1,
              },
            ]" @mouseenter="contentColor(index)" @mouseleave="contentLeave" @click="proDetail(item)">
              <div style="display: flex; margin: 24px">
                <div>
                  <img v-if="item.image" v-lazy="`${item.image}`" style="width: 168px; height: 105px" />
                  <img src="@/assets/images/home/<USER>" style="width: 168px; height: 105px" v-else />
                </div>
                <div class="card_center">
                  <div class="card_text">
                    <div class="card_tag">
                      <a-tag color="#D7E6FF" v-if="item.labelName">{{
                        item.labelName
                      }}</a-tag>
                      <div class="card_title">{{ item.name }}</div>
                    </div>
                    <div class="flex" v-if="item.provider">
                      <a-tag :bordered="false" class="cityStyle" style="margin-right: 0">{{ item.provider }}</a-tag>
                    </div>
                  </div>
                  <div class="card_des">
                    {{ item.introduction }}
                  </div>
                  <!-- <div class="flex" style="justify-content: space-between">
                    <div class="flex">
                      <a-tag
                        color="#D7E6FF"
                        style="
                          display: block;
                          color: rgba(0, 0, 0, 0.45);
                          background-color: transparent;
                          border: 1px solid #d9d9d9;
                          line-height: 17px;
                        "
                        >{{ item.label[0] }}</a-tag
                      >
                      <a-tag
                        color="#D7E6FF"
                        v-if="item.label[1]"
                        style="
                          display: block;
                          color: rgba(0, 0, 0, 0.45);
                          background-color: transparent;
                          border: 1px solid #d9d9d9;
                          line-height: 17px;
                        "
                        >{{ item.label[1] }}</a-tag
                      >
                    </div>
                  </div> -->
                  <div style="
                      display: flex;
                      align-items: center;
                      justify-content: space-between;
                    ">
                    <div style="
                        display: flex;
                        align-items: center;
                        justify-content: space-between;
                      "></div>
                  </div>
                  <div style="
                      display: flex;
                      align-items: center;
                      justify-content: space-between;
                      padding-right: 6px;
                    ">
                    <div style="display: flex; align-items: center">
                      <img src="@/assets/images/home/<USER>" style="width: 16px; height: 16px" />
                      <span style="font-size: 12px; color: rgba(0, 0, 0, 0.45)" v-if="item.viewCount">{{ item.viewCount
                        }}</span>
                      <span v-else>-</span>
                      <!-- <img
                        src="@/assets/images/home/<USER>"
                        style="width: 16px; height: 16px; margin-left: 18px"
                      />
                      <span
                        style="font-size: 12px; color: rgba(0, 0, 0, 0.45)"
                        v-if="item.downloadCount"
                        >{{ item.downloadCount }}</span
                      >
                      <span v-else>-</span> -->
                    </div>
                    <div style="display: flex">
                      <div v-if="item.existsPpt">
                        <div v-if="item.addCart">
                          <button disabled class="cart-button">
                            <span class="add" style="color: rgba(0, 0, 0, 0.9)">
                              &nbsp;已加入预选</span>
                          </button>
                        </div>
                        <div v-else>
                          <button class="cart-button pointer" @click.stop="addShopping(item.id)">
                            <img class="add-icon" src=" @/assets/images/AI/isadded.png" /><span class="add">
                              &nbsp;加入预选</span>
                          </button>
                        </div>
                      </div>
                      <div style="margin-left: 20px">
                        <div v-if="item.addOrder">
                          <button disabled class="cart-button">
                            <span class="add" style="color: rgba(0, 0, 0, 0.9)">
                              &nbsp;已加入定制</span>
                          </button>
                        </div>
                        <div v-else>
                          <button class="cart-button pointer" @click.stop="add(item.id)">
                            <img class="add-icon" src=" @/assets/images/AI/isadded.png" /><span class="add">
                              &nbsp;加入定制</span>
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </template>
        </div>
      </div>
      <div class="layPage">
        <a-pagination v-model:pageSize="pageItemSize" v-model:current="currentPage" :pageSizeOptions="pageSizeOptions"
          show-quick-jumper show-size-changer :total="totalItemCount" @change="pageChange" @showSizeChange="sizeChange"
          class="mypage" />
      </div>
    </div>
    <div v-if="tableList && tableList.length == 0" class="emptyPhoto">
      <img src="@/assets/images/home/<USER>" />
    </div>
    <div class="loading" v-show="loadingShow">
      <a-spin />
    </div>
  </div>
</template>

<script>
import { defineComponent, reactive, toRefs, ref } from "vue";
import { useRouter } from "vue-router";
import { getProductList } from "@/api/product/home";

import zhCN from "ant-design-vue/es/locale/zh_CN";
import { getNewLabel } from "@/api/product/home";
import { getMakeUrl } from "@/utils/getUrl";
import eventBus from "@/utils/eventBus";
import { AISearch } from "@/api/AI/ai.js";
import { useHomeStore } from "@/store";
import { addShop } from "@/api/buyList/index.js";
import { addShoppingCart } from "@/api/combine/shoppingCart.js";
import loadingSmall from "@/components/superLoadingSmall/loadingSmall.vue";

export default defineComponent({
  components: { loadingSmall },
  setup() {
    const counterStore = useHomeStore();
    const baseURL = getMakeUrl();
    const vocation = ref("5");
    const region = ref("");
    const box = ref(null);
    const selectData = ref(null);
    const data = reactive({
      name: "",
      moment: "",
      loadingShow: true,
      activeKey: "",
      activeKeyAbility: "",
      cardActive: "-1",
      pageSizeOptions: ["10", "20", "30", "50"],
      totalItemCount: 0,
      AIsearch: false,
      labelIdlist: [],

      labelList: [],
      tableList: [],
      showLabel: true,
      totalNum: 0,
      totalItemCount1: 0,
      currentPage: 1,
      pageItemSize: 10,
      showLast: false,
      showId: undefined,
      selectList: [],
      selectListNew: [],
      showMore: false,
      classSelect: "",
      typeSelect: "",
      labeSelect: "",
      showScense: "",
      morePro: true,
      labelPro: true,
      abilityPro: true,
      providerSelect: [],
      abilitySelect: [],
      showIndex: "",
      showPagination: true,
      switchOnOff: true,
      productType: "",
      productClassList: [],
      productTypeList: [],
      productLabelList: [],
      productLabel: "",
      tableAIAllList: [],
    });
    const getList = () => {
      let pageParams = {
        pageNo: data.currentPage,
        pageSize: data.pageItemSize,
        keyword: data.name,
        classifyIds: data.activeKey,
        labelIds: data.productLabel,
        typeIds: data.productType,
      };
      data.loadingShow = true;
      getProductList(pageParams)
        .then((res) => {
          data.showPagination = true;
          data.loadingShow = false;
          data.tableList = [];
          data.tableList = res.data.rows;
          data.totalItemCount = res.data.totalRows;

          data.tableList.map((item) => {
            item.provider = item.provider.split("/")[0];
          });

          if (data.activeKey == "") {
            data.totalItemCount1 = res.data.totalRows;
          }
        })
        .catch((error) => {
          data.loadingShow = false;
        });
    };
    const getLabel = () => {
      let params = {
        pageSize: 100,
        pageNo: 1,
      };
      getNewLabel(params).then((res) => {
        data.productClassList = res.data.rows.map((item) => ({
          label: item.name,
          value: item.id,
          length: item.children ? item.children.length : 0,
          children: item.children
            ? item.children.map((child) => ({
              label: child.name,
              value: child.id,
              children: undefined,
            }))
            : undefined,
        }));
        console.log(data.productClassList);

        let productTypeListTemp = [];
        res.data.rows.forEach((item) => {
          if (item.children) {
            item.children.forEach((child) => {
              productTypeListTemp.push({
                label: child.name,
                value: child.id,
              });
            });
          }
        });
        data.productTypeList = productTypeListTemp;
        let productLabelListTemp = [];
        res.data.rows.forEach((item) => {
          if (item.children) {
            item.children.forEach((child) => {
              if (child.children) {
                child.children.forEach((grandChild) => {
                  productLabelListTemp.push({
                    label: grandChild.name,
                    value: grandChild.id,
                  });
                });
              }
            });
          }
        });
        data.productLabelList = productLabelListTemp;
      });
    };
    getLabel();
    const labelChange = (val) => {
      data.labelIdlist = val.join(",");
    };
    getList();

    const seekContent = () => {
      data.currentPage = 1;
      if (data.switchOnOff) {
        getAIList();
      } else {
        getList();
      }
    };
    const regionChange = (val) => {
      region.value = val;
      getList();
    };
    const tabChange = (val) => {
      if (val.value !== data.activeKey) {
        data.activeKey = val.value;
        data.currentPage = 1;
        getList();
      }
    };
    const contentColor = (index) => {
      data.cardActive = index;
    };
    const router = useRouter();
    const proDetail = (val) => {
      router.push({
        query: {
          id: val.id,
        },
        name: "productDetail",
      });
    };
    const contentLeave = () => {
      data.cardActive = "-1";
    };

    const add = (id) => {
      addShop({
        productId: id.toString(),
        type: "2",
      }).then((res) => {
        if (data.showPagination) {
          getList();
        } else {
          //getAIList();
        }
      });
      eventBus.emit("cartRefresh");
      counterStore.contralShop = false;
    };
    const addShopping = (id) => {
      addShoppingCart({
        schemeId: id.toString(),
        type: "4",
      }).then((res) => {
        if (data.showPagination) {
          getList();
        } else {
          //getAIList();
        }
      });
      eventBus.emit("cartRefresh");
      counterStore.contralShop = false;
    };

    const getAIPageList = () => {
      let firstPageNum = (data.currentPage - 1) * data.pageItemSize;
      let lastPageNum = data.currentPage * data.pageItemSize;
      data.tableList = data.tableAIAllList.slice(firstPageNum, lastPageNum);
    };
    const pageChange = (page, pageSize) => {
      data.currentPage = page;
      if (data.showPagination) {
        getList();
      } else {
        getAIPageList();
      }
    };
    const sizeChange = (current, size) => {
      data.pageItemSize = size;
      if (data.showPagination) {
        getList();
      } else {
        getAIPageList();
      }
    };

    const labelSelect = (value, type = "default", index) => {
      if (value.children && type !== "last") {
        data.showLast = !data.showLast;
        data.showId = value.value;
        data.showScense = "label";
        data.showIndex = index;
      } else {
        if (data.selectList.includes(value.label)) {
          const index = data.selectList.findIndex(
            (item) => item === value.label
          );
          if (index !== -1) {
            data.selectList.splice(index, 1);
          }
          const index1 = data.selectListNew.findIndex(
            (item) => item === value.value
          );
          if (index1 !== -1) {
            data.selectListNew.splice(index, 1);
          }
        } else {
          data.selectList.push(value.label);
          data.selectListNew.push(value.value);
        }
        data.selectList = data.selectList.filter((value, index, self) => {
          return self.indexOf(value) === index;
        });
        data.selectListNew = data.selectListNew.filter((value, index, self) => {
          return self.indexOf(value) === index;
        });
        data.labelIdlist = data.selectListNew;
        data.labelIdlist = data.labelIdlist.join(",");
        getList();
      }
    };
    const providerBtn = (val, type) => {
      if (type === "class") {
        if (data.activeKey !== val.value) {
          data.activeKey = val.value;
          data.classSelect = val.label;
        } else {
          data.activeKey = "";
          data.classSelect = "";
        }
      }
      if (type === "type") {
        if (data.productType !== val.value) {
          data.productType = val.value;
          data.typeSelect = val.label;
        } else {
          data.productType = "";
          data.typeSelect = "";
        }
      }
      if (type === "label") {
        if (data.productLabel !== val.value) {
          data.productLabel = val.value;
          data.labeSelect = val.label;
        } else {
          data.productLabel = "";
          data.labeSelect = "";
        }
      }
      data.currentPage = 1;
      getList();
    };
    const deleteSelect = (type) => {
      if (type === "class") {
        data.classSelect = "";
        data.activeKey = "";
      }
      if (type === "type") {
        data.productType = "";
        data.typeSelect = "";
      }
      if (type === "label") {
        data.productLabel = "";
        data.labeSelect = "";
      }
      getList();
    };
    const showMore = (type, index) => {
      if (type == "productType") {
        data.showScense = type;
        data.morePro = false;
      } else if (type === "productLabe") {
        data.showScense = type;
        data.labelPro = false;
      } else {
        data.showIndex = index;
        data.showScense = type;
      }
    };
    const showLess = (type, index) => {
      if (type == "provider_less") {
        data.showScense = type;
        data.morePro = true;
      } else if (type == "productLabe_less") {
        data.showScense = type;
        data.labelPro = true;
      } else {
        data.showIndex = "";
        data.showScense = type;
      }
    };

    const getAIList = () => {
    	if (data.name == "") {
    		data.showPagination = true;
    		getList();
    		return false
    	}
      data.loadingShow = true;
      data.AIsearch = true;
      AISearch({
        question: data.name,
        type: 6,
      }).then((res) => {
        data.loadingShow = false;
        data.AIsearch = false;
        if (res.code == 200) {
          data.showPagination = false;
          data.tableList = [];
          data.tableAIAllList = [];
          data.tableAIAllList = res.data;
          getAIPageList();
          // data.tableList = data.tableAIAllList.slice(0, 10);
          data.totalItemCount = res.data ? res.data.length : 0;
          data.tableList.map((item) => {
            item.provider = item.provider.split("/")[0];
          });
        }
      });
    };
    const refreshList = () => {
      if (!data.switchOnOff) {
        getList();
      } else {
        getAIList();
      }
    };
    
    eventBus.on("productRefresh", refreshList);

    const toggleShowLabel = () => {
      data.showLabel = !data.showLabel;
    };
    
    const getBoxTitle = (key) => {
    	const rect = box.value[key].getBoundingClientRect();
    	const selectHtml = selectData.value.getBoundingClientRect();
      return {
        left: (rect.x - selectHtml.x) + "px",
        top: (rect.y - selectHtml.y) + "px"
      };
    }

    const getBoxLeft = (key) => {
      const rect = box.value[key].getBoundingClientRect();
      const firstRect = box.value[0].getBoundingClientRect();
      const selectHtml = selectData.value.getBoundingClientRect();
      return {
        left: (firstRect.x - selectHtml.x) + "px",
        top: (rect.y - selectHtml.y + 49) + "px"
      };
    }

    return {
      ...toRefs(data),
      vocation,
      toggleShowLabel,
      region,
      labelSelect,
      providerBtn,
      showMore,
      showLess,
      deleteSelect,
      regionChange,
      tabChange,
      add,
      addShopping,
      contentColor,
      labelChange,
      getLabel,
      sizeChange,
      contentLeave,
      proDetail,
      router,
      pageChange,
      zhCN,
      seekContent,
      baseURL,
      getAIList,
      refreshList,
      counterStore,
      getBoxTitle,
      getBoxLeft
    };
  },
});
</script>

<style lang="scss" scoped src="./tableList.scss"></style>

<style lang="scss">
.mypage {
  .ant-pagination {
    color: rgba(0, 0, 0, 0.65);
  }

  .ant-select-selector {
    color: rgba(0, 0, 0, 0.65);
  }

  .ant-pagination-item-active {
    background: #007eff;
  }

  .ant-pagination-item-active a {
    color: #ffffff;
  }

  .ant-pagination-item-active:focus a,
  .ant-pagination-item-active:hover a {
    color: #ffffff;
  }

  .ant-pagination-item-active a:focus,
  .ant-pagination-item-active a:hover {
    color: #ffffff;
  }

  .ant-pagination-item:focus,
  .ant-pagination-item:hover {
    background: #007eff;
  }

  .ant-pagination-item:focus a,
  .ant-pagination-item:hover a {
    color: #ffffff;
  }

  .ant-pagination-item-disabled:hover,
  .ant-pagination-item-disabled:focus {
    background-color: #ffffff;
    border-color: #d9d9d9;

    a {
      color: rgba(0, 0, 0, 0.25);
    }
  }

  .ant-pagination-options-size-changer.ant-select {
    margin-right: 0;
  }

  .ant-pagination-options-quick-jumper {
    margin-left: 8px;
  }
}

/*.shopping {
  position: absolute;
  right: 4px;
  bottom: 12px;
}*/

.ant-select:not(.ant-select-customize-input) .ant-select-selector {
  border: none !important;
}

.custom-placeholder .ant-select-selection-placeholder {
  color: #000;
}

.AISearchLogo {
  width: 190px;
  height: 80px;

  img {
    width: 100%;
    height: 100%;
  }
}
</style>
