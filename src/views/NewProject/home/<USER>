<template>
  <!-- 多轮对话草图 -->
  <div class="home flex">
    <!-- 公告通知 -->
    <notice-list />
    <div style="padding-top: 180px; flex: 1;">
      <div class="logoStyle maxWidth flex just-center align-center margin_b_48" style="">
        <img src="@/assets/images/newProject/bigLogo.png" />
      </div>
      <div class="introduce flex just-center maxWidth">
        Hi，我是你的智能助手小麟，你想定制方案/场景，或者是查询方案/能力/产品/场景吗？
      </div>
      <div class="writing">
        <div class="newSearchInfo">
          <div class="vocationPull maxWidth maxHeight">
            <a-textarea ref="textareaRef" v-model:value="data.name" :disabled="data.isAIAllSearch" class="inputClass"
              style="min-height: 60px; font-size: 14px" allow-clear auto-size @keydown.enter.prevent="seekContent1"
              placeholder="请问有什么可以帮您的吗？" />
            <div class="btns flex align-center">
              <!-- <new-voiceRecorder ref="recorderCom" style="
                height: 44px;
                margin-right: 20px;
              " :isTranslating="isTranslating" :canBtnUse="canBtnUse" @goSearch="seekContent1"
                @audioReady="handleAudio" @changeIsListenStatus="changeIsListenStatus"></new-voiceRecorder> -->
                <!-- 暂时隐藏全量检索 -->
              <!-- <a-tooltip placement="top">
                <template #title>
                  <p class="text-center">在所有知识库中查找相关内容</p>
                  <p class="text-center">（查询量较大，请您耐心等待）</p>
                </template>
                <div class="AIAllSearch flex align-center just-center margin_r_20 pointer" @click="AIAllSearch()">
                  <img class="margin_r_4" :src=data.AIIcon alt="">
                  <div class="AItext">{{ data.AItext }}</div>
                </div>
              </a-tooltip> -->
              <img class="pointer" style="width: 24px;height: 24px;margin-right: 20px;" :src=data.voiceImg
                @click="openMicphoneModal" alt="">
              <img class="sendbtn pointer" :src=data.sendImg @click="seekContent1()" alt="">
            </div>
          </div>
          <div v-if="data.showThinkingSwitch" class="thinking flex align-center margin_t_10">
            <div class="thinkingBtnOpen">定制深度思考</div>
            <a-tooltip placement="top">
              <template #title>
                <span>利用深度思考,为行业方案扩展内容,但这通常会消耗更长的时间。</span>
              </template>
              <svg t="1750293864135" class="icon margin_r_10" viewBox="0 0 1024 1024" version="1.1"
                xmlns="http://www.w3.org/2000/svg" p-id="3229" width="24" height="24">
                <path
                  d="M544 581.696v15.488a32 32 0 1 1-64 0V560c0-14.848 10.304-26.752 24-30.4 72.032-30.08 72.064-74.688 72-76.608l-0.032-49.184c0-28.544-28.704-51.84-64-51.84-35.264 0-63.968 23.296-63.968 51.84v15.584a32 32 0 0 1-64 0v-15.584c0-63.84 57.408-115.84 127.968-115.84 70.592 0 128 52 128 115.84v47.584c0.16 1.28 4.672 80.768-95.968 130.304M512 736a32 32 0 1 1 0-64 32 32 0 0 1 0 64m0-608C300.256 128 128 300.288 128 512c0 211.744 172.256 384 384 384s384-172.256 384-384c0-211.712-172.256-384-384-384"
                  fill="#24456A" p-id="3230"></path>
              </svg>
            </a-tooltip>

            <a-switch v-model:checked="data.thinkingStatus" />
          </div>
          <!-- 数字滚动 -->
          <div class="dataScreen flex just-sa">
            <div class="eachData flex just-center align-center" v-for="item in data.titleItem" :key="item.title">
              <div>
                <div class="eachDataNumber text-center">
                  <number-scroll :value="item.number" :duration="1000" />
                </div>
                <p class="eachDataTitle text-center">{{ item.title }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <a-modal class="micModal" v-if="data.micphoneVisible" v-model:visible="data.micphoneVisible" :width="1056"
      :footer="null" :maskClosable="false" @cancel="closeMicModal()" :style="{ top: '30%' }">
      <div class="content maxWidth" style="padding: 8px 40px 8px 40px">
        <div class="flex">
          <div class="margin_b_32 margin_r_40">
            <img style="width: 88px;height: 88px;" src="../../../assets/images/AI/voiceMic.png" alt="">
          </div>
          <div>
            <img v-if="!data.isSpeaking" style="width: 532px;height: 88px;"
              src="../../../assets/images/AI/blackSpeaking.png" alt="">
            <img v-else style="width: 532px;height: 88px;" src="../../../assets/images/AI/speaking.gif" alt="">
          </div>
        </div>

        <div class="micTextWords flex align-center just-center margin_b_32">
          <a-textarea v-model:value="data.micWords" :disabled="false" class="inputClass" :resize="'none'" auto-size
            placeholder="语音实时转文字内容..." style="
                font-size: 16px;
                width: 928px;
                min-height: 185px;
                border: none;
                border-radius: 10px;
                background: rgba(255,255,255,0.3);
                color: #000;
                border: 2px solid #FFFFFF;
              " />
        </div>
        <div class="micLogo flex just-center align-center maxWidth">
          <new-voiceRecorder ref="recorderCom" @startSpeaking="startSpeaking" @audioReady="handleAudio"
            @getVoiceWord="getVoiceWord"></new-voiceRecorder>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { reactive, ref, onMounted, onUnmounted, watch } from "vue";
import { useRoute, useRouter } from "vue-router";
import newVoiceRecorder from "@/components/voiceRecorder/newVoiceRecorder.vue";
import { getHotSearch, deleteAISearchHistory } from "@/api/AI/ai.js";
import solutionIcon from "../../../assets/images/AI/solutionIcon.png"
import abilityIcon from "../../../assets/images/AI/abilityIcon.png"
import productIcon from "../../../assets/images/AI/productIcon.png"
import customizeIcon from "../../../assets/images/AI/customizeIcon.png"
import { getSummaryData, getRetrieveList, getFullRetrieveList } from "@/api/AI/ai.js";
import eventBus from "@/utils/eventBus";
import NumberScroll from '@/components/NumberScroll.vue';
import AIAllSearchIcon from '../../../assets/images/AI/AIAllSearch.png'
import loadingOverIcon from '../../../assets/images/AI/loadingOver.png'
import AIloadingIcon from '../../../assets/images/AI/AIloadingIcon.png'
import noticeList from "./components/noticeList.vue"
import voiceIcon from '../../../assets/images/AI/microphone.png'
import sendIcon from '../../../assets/images/AI/goSearchBtn.png'
import voiceDisable from '../../../assets/images/AI/voiceDisable.png'
import sendDisable from '../../../assets/images/AI/sendDisable.png'
import { set } from "vue-demi";
const recorderCom = ref(null);
const router = useRouter();
const textareaRef = ref(null);
const data = reactive({
  showThinkingSwitch: false,
  thinkingStatus: true,
  name: "",
  isTranslating: false,
  canBtnUse: false,
  micWords: null,//以下是用于语音输入的
  micphoneVisible: false,
  isSpeaking: false,
  titleItem: [
    {
      type: 'solution',
      number: 0,
      title: '方案数量'
    },
    {
      type: 'ability',
      number: 0,
      title: '能力数量'
    },
    {
      type: 'product',
      number: 0,
      title: '产品数量'
    },
    {
      type: 'resource',
      number: 0,
      title: '资源查阅(次)'
    },
    {
      type: 'aiCustomize',
      number: 0,
      title: 'AI定制(次)'
    },
  ],
  voiceImg: voiceIcon,
  sendImg: sendIcon,
  AItext: 'AI全量检索',
  AItextIndex: 0,
  AIIcon: AIAllSearchIcon,
  isAIAllSearch: false,
  goSearchPageList: {
    solutionList: [],
    abilityList: [],
    sceneList: [],
    productList: [],
    productPackageList: []
  },
});


// 老语音输入
// const handleAudio = (text) => {
//   console.log("text", text);
//   data.name = text;
// };
// 新语音输入
const openMicphoneModal = () => {
  if (data.isAIAllSearch) {
    return
  }
  data.micphoneVisible = true;
  data.micWords = null;
};
const closeMicModal = () => {
  console.log("1");
  // data.micWords = null
  data.isSpeaking = false;
  data.micphoneVisible = false;
  // console.log('connectWebSocket')
  recorderCom.value.closeConnection();
};
const startSpeaking = () => {
  data.isSpeaking = true
}
const handleAudio = (text) => {
  console.log("text", text);
  data.micWords = text;
  handleTextareaChange()
};
const getVoiceWord = (words) => {
  data.micphoneVisible = false
  data.name = data.micWords
  data.isSpeaking = false
  handleTextareaChange()
}

const seekContent1 = () => {
  if (data.isAIAllSearch) {
    return
  }
  localStorage.setItem("isFromHistory", '不是')
  // let question = JSON.parse(JSON.stringify(data.name.trim()))
  if (data.name.trim() !== '') {
    localStorage.setItem("AISearchQuestion", data.name)
    localStorage.setItem("isFromHome", '是')
    // 跳转到新的页面去
    router.push({
      path: "AISearch",
      query: {
        thinkingStatus: data.thinkingStatus
      }
    });
    eventBus.emit('setTemporaryHistory', data.name, '')
    // // 点击搜索关闭webscoket连接
    if (recorderCom.value) {
      console.log("关闭webscoket连接");
      recorderCom.value.closeConnection();
    }
  }

};
const toSearch = (value) => {
  data.name = value;
  seekContent1();
};


// 更新数字的函数
const updateNumbers = () => {
  getSummaryData().then(res => {
    console.log("res", res);
    data.titleItem = data.titleItem.map(item => {
      let newNumber;
      let newTitle;
      // 根据不同类型设置不同的数值范围
      switch (item.type) {
        case 'solution':
          if (res.data.solutionData && res.data.solutionData < 10000) {
            newNumber = res.data.solutionData;
            newTitle = '方案数量';
          } else if (res.data.solutionData && res.data.solutionData >= 10000) {
            newNumber = (res.data.solutionData / 10000).toFixed(2);
            newTitle = '方案数量';
          }
          break;
        case 'ability':
          if (res.data.abilityData && res.data.abilityData < 10000) {
            newNumber = res.data.abilityData;
            newTitle = '能力数量';
          } else if (res.data.abilityData && res.data.abilityData >= 10000) {
            newNumber = (res.data.abilityData / 10000).toFixed(2);
            newTitle = '能力数量';
          }
          break;
        case 'product':
          if (res.data.productData && res.data.productData < 10000) {
            newNumber = res.data.productData;
            newTitle = '产品数量';
          } else if (res.data.productData && res.data.productData >= 10000) {
            newNumber = (res.data.productData / 10000).toFixed(2);
            newTitle = '产品数量';
          }
          break;
        case 'resource':
          if (res.data.resourceData && res.data.resourceData < 10000) {
            newNumber = res.data.resourceData;
            newTitle = '资源查阅(次)';
          } else if (res.data.resourceData && res.data.resourceData >= 10000) {
            newNumber = (res.data.resourceData / 10000).toFixed(2);
            newTitle = '资源查阅(万次)';
          }
          break;
        case 'aiCustomize':
          if (res.data.aiCustomizeData && res.data.aiCustomizeData < 10000) {
            newNumber = res.data.aiCustomizeData;
            newTitle = 'AI定制(次)';
          } else if (res.data.aiCustomizeData && res.data.aiCustomizeData >= 10000) {
            newNumber = (res.data.aiCustomizeData / 10000).toFixed(2);
            newTitle = 'AI定制(万次)';
          }
          break;
        default:
          newNumber = item.number;
          newTitle = item.title;
      }
      return {
        ...item,
        number: Number(newNumber),
        title: newTitle
      }
    })
  })

}
const handleTextareaChange = () => {
  console.log("textareaRef.value", textareaRef.value);
  textareaRef.value.focus()
}
let timer = null

onMounted(() => {
  let userInfo = JSON.parse(localStorage.getItem("userInfo"));
  if (userInfo?.versionPopupShowed == 1) {
    eventBus.emit('noticeModal')
  }
  localStorage.removeItem('isFromHome')
  localStorage.removeItem('fromHomeSessionId')
  // 初始更新一次
  updateNumbers()
  // 设置定时器,每5秒更新一次
  timer = setInterval(updateNumbers, 60000)
  handleTextareaChange()

  // 监听showThinkingSwitch事件
  eventBus.on('showThinkingSwitch', handleShowThinkingSwitch);
})

onUnmounted(() => {
  // 组件卸载时清除定时器
  if (timer) {
    clearInterval(timer)
  }

  // 移除事件监听
  eventBus.off('showThinkingSwitch', handleShowThinkingSwitch);
})

// 监听thinkingStatus的变化
watch(() => data.thinkingStatus, (newVal, oldVal) => {
  // 每次切换时都将showThinkingSwitch设置为false
  data.showThinkingSwitch = false;
})

// 在methods中添加处理方法
const handleShowThinkingSwitch = () => {
  // 将showThinkingSwitch设置为true
  data.showThinkingSwitch = true;
}
const AIAllSearch = () => {
  if (data.name.trim() !== '') {
    if (!data.isAIAllSearch) {
      data.isAIAllSearch = true
      data.AItext = ''
      data.AIIcon = AIloadingIcon
      data.voiceImg = voiceDisable
      data.sendImg = sendDisable
      let text = data.name + '全量检索中，请稍后...'
      return new Promise((resolve) => {
        const write = () => {
          if (data.AItextIndex < text.length) {
            data.AItext += text.charAt(data.AItextIndex);
            data.AItextIndex++;
            setTimeout(write, 50);
          } else {
            resolve();
            data.AItextIndex = 0
            getAISearchResultSolution()
          }
        };
        write();
      });
    } else {
      return
    }
  }
}
// 搜方案的接口
const getAISearchResultSolution = () => {
  // 调接口
  getFullRetrieveList({
    question: data.name,
  }).then((res) => {
    console.log("res", res);

    data.AItext = ''
    data.AIIcon = loadingOverIcon
    let text = data.name + '全量检索完成'
    return new Promise((resolve) => {
      const write = () => {
        if (data.AItextIndex < text.length) {
          data.AItext += text.charAt(data.AItextIndex);
          data.AItextIndex++;
          setTimeout(write, 50);
        } else {
          resolve();
          data.AItextIndex = 0
          localStorage.setItem("goSearchPageList", JSON.stringify(res.data));
          let href = window.location.origin + '/#/newProject/newProject?type=6'
          console.log('href', href)
          window.open(href, '_blank')
          AIAllSearchOver()
        }
      };
      write();
    });
  })
}
// 搜原子能力的文字
const writingAbiltySearchText = () => {
  data.isAIAllSearch = true
  data.AItext = ''
  data.AIIcon = AIloadingIcon
  let text = data.name + '原子能力检索中，请稍后...'
  return new Promise((resolve) => {
    const write = () => {
      if (data.AItextIndex < text.length) {
        data.AItext += text.charAt(data.AItextIndex);
        data.AItextIndex++;
        setTimeout(write, 50);
      } else {
        resolve();
        data.AItextIndex = 0
        getAISearchResultAbility()
      }
    };
    write();
  });
}
// 搜原子能力的接口
const getAISearchResultAbility = () => {
  // 调接口
  getRetrieveList({
    knowledgeType: 2,
    description: data.name,
    topK: 10,
    name: data.name,
  }).then((res) => {
    console.log("res", res);
    data.goSearchPageList.abilityList.push({
      key: data.name,
      knowledgeType: '能力',
      ability: res.data.dataList,
      priority: res.data.priority,
    });
    data.AItext = ''
    data.AIIcon = loadingOverIcon
    let text = data.name + '原子能力检索完成'
    return new Promise((resolve) => {
      const write = () => {
        if (data.AItextIndex < text.length) {
          data.AItext += text.charAt(data.AItextIndex);
          data.AItextIndex++;
          setTimeout(write, 50);
        } else {
          resolve();
          data.AItextIndex = 0
          setTimeout(() => {
            writingSceneSearchText()
          }, 400);
        }
      };
      write();
    });
  })
}
// 搜场景方案的文字
const writingSceneSearchText = () => {
  data.isAIAllSearch = true
  data.AItext = ''
  data.AIIcon = AIloadingIcon
  let text = data.name + '场景方案检索中，请稍后...'
  return new Promise((resolve) => {
    const write = () => {
      if (data.AItextIndex < text.length) {
        data.AItext += text.charAt(data.AItextIndex);
        data.AItextIndex++;
        setTimeout(write, 50);
      } else {
        resolve();
        data.AItextIndex = 0
        getAISearchResultScene()
      }
    };
    write();
  });
}
// 搜场景方案的接口
const getAISearchResultScene = () => {
  // 调接口
  getRetrieveList({
    knowledgeType: 3,
    description: data.name,
    topK: 10,
    name: data.name,
  }).then((res) => {
    console.log("res", res);
    data.goSearchPageList.sceneList.push({
      key: data.name,
      knowledgeType: '场景',
      scene: res.data.dataList,
      priority: res.data.priority,
    });
    data.AItext = ''
    data.AIIcon = loadingOverIcon
    let text = data.name + '场景方案检索完成'
    return new Promise((resolve) => {
      const write = () => {
        if (data.AItextIndex < text.length) {
          data.AItext += text.charAt(data.AItextIndex);
          data.AItextIndex++;
          setTimeout(write, 50);
        } else {
          resolve();
          data.AItextIndex = 0
          setTimeout(() => {
            writingProductSearchText()
          }, 400);
        }
      };
      write();
    });
  })
}
// 搜标准产品的文字
const writingProductSearchText = () => {
  data.isAIAllSearch = true
  data.AItext = ''
  data.AIIcon = AIloadingIcon
  let text = data.name + '标准产品检索中，请稍后...'
  return new Promise((resolve) => {
    const write = () => {
      if (data.AItextIndex < text.length) {
        data.AItext += text.charAt(data.AItextIndex);
        data.AItextIndex++;
        setTimeout(write, 50);
      } else {
        resolve();
        data.AItextIndex = 0
        getAISearchResultProduct()
      }
    };
    write();
  });
}
// 搜标准产品的接口
const getAISearchResultProduct = () => {
  // 调接口
  getRetrieveList({
    knowledgeType: 5,
    description: data.name,
    topK: 10,
    name: data.name,
  }).then((res) => {
    console.log("res", res);
    data.goSearchPageList.productList.push({
      key: data.name,
      knowledgeType: '产品',
      product: res.data.dataList,
      priority: res.data.priority,
    });
    data.AItext = ''
    data.AIIcon = loadingOverIcon
    let text = data.name + '标准产品检索完成'
    return new Promise((resolve) => {
      const write = () => {
        if (data.AItextIndex < text.length) {
          data.AItext += text.charAt(data.AItextIndex);
          data.AItextIndex++;
          setTimeout(write, 50);
        } else {
          resolve();
          data.AItextIndex = 0
          setTimeout(() => {
            writingProductBagSearchText()
          }, 400);
        }
      };
      write();
    });
  })
}
// 搜商客场景的文字
const writingProductBagSearchText = () => {
  data.isAIAllSearch = true
  data.AItext = ''
  data.AIIcon = AIloadingIcon
  let text = data.name + '商客场景检索中，请稍后...'
  return new Promise((resolve) => {
    const write = () => {
      if (data.AItextIndex < text.length) {
        data.AItext += text.charAt(data.AItextIndex);
        data.AItextIndex++;
        setTimeout(write, 50);
      } else {
        resolve();
        data.AItextIndex = 0
        getAISearchResultProductBag()
      }
    };
    write();
  });
}
// 搜商客场景的接口
const getAISearchResultProductBag = () => {
  // 调接口
  getRetrieveList({
    knowledgeType: 4,
    description: data.name,
    topK: 10,
    name: data.name,
  }).then((res) => {
    console.log("res", res);
    data.goSearchPageList.productPackageList.push({
      key: data.name,
      knowledgeType: '产品包',
      productBag: res.data.dataList,
      priority: res.data.priority,
    });
    data.AItext = ''
    data.AIIcon = loadingOverIcon
    let text = data.name + '商客场景检索完成'
    return new Promise((resolve) => {
      const write = () => {
        if (data.AItextIndex < text.length) {
          data.AItext += text.charAt(data.AItextIndex);
          data.AItextIndex++;
          setTimeout(write, 50);
        } else {
          resolve();
          console.log('data.goSearchPageList', data.goSearchPageList)
          localStorage.setItem("goSearchPageList", JSON.stringify(data.goSearchPageList));
          let href = window.location.origin + '/#/newProject/newProject?type=6'
          console.log('href', href)
          window.open(href, '_blank')
          AIAllSearchOver()
        }
      };
      write();
    });
  })
}
// 全量检索结束
const AIAllSearchOver = () => {
  data.AItext = 'AI全量检索'
  data.AItextIndex = 0
  data.isAIAllSearch = false
  data.goSearchPageList = {
    solutionList: [],
    abilityList: [],
    sceneList: [],
    productList: [],
    productPackageList: []
  }
  data.AIIcon = AIAllSearchIcon
  data.voiceImg = voiceIcon
  data.sendImg = sendIcon
}
</script>

<style lang="scss" scoped>
// 添加工具类
.flex {
  display: flex;
}

.just-sa {
  justify-content: space-around;
}

.just-center {
  justify-content: center;
}

.align-center {
  align-items: center;
}

.home {
  // padding-top: 7vh;
  height: calc(100vh - 60px);
  background-image: url(../../../assets/images/AI/newHomeBg.png);
  background-size: cover;
  position: relative;
  z-index: -2;

}


.words {
  width: 100%;
  margin: 0 auto;

  .eachText {
    background: #ffffff;
    border-radius: 10px 10px 10px 10px;
    border: 1px solid #e5e5e5;
    padding: 12px 24px;
    font-weight: 400;
    font-size: 16px;
    color: rgba(131, 119, 119, 0.65);
    line-height: 16px;
    cursor: pointer;
    box-shadow: 0px 8px 16px rgba(116, 157, 219, 0.3);
  }
}

.top_con {
  // padding: 0 262px;
  margin-top: 12px;
}


.introduce {
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 400;
  font-size: 20px;
  color: rgba(36, 69, 106, 0.65);
}

.writing {
  margin: auto;
  width: 1090px;
  height: 228px;
  // background-image: url(../../../assets/images/AI/writingBg.png);
  background-size: 100% 100%;
  padding: 10px;

  .newSearchInfo {
    background-repeat: no-repeat;
    background-size: 100% 100%;
    width: 960px;
    height: 128px;
    margin: 0 auto;
    transition: 1s;
    position: relative;

    .vocationPull {
      background: #F5F7FC;
      border-radius: 16px;
      overflow: hidden;
      padding-left: 10px;
      border: 1px solid #e5e5e5;
    }

    .inputClass {
      border: none;
      width: 98%;
      height: 65%;
      box-shadow: none !important;
      background-color: transparent;
    }

    .btns {
      width: 100%;
      height: 35%;
      justify-content: flex-end;
      padding-right: 20px;
      padding-bottom: 8px;

      .sendbtn {
        width: 32px;
        height: 32px;
      }

      .AIAllSearch {
        height: 32px;
        padding: 5px 12px;
        border: 1px solid #8696FF;
        background-color: #ECEDFF;
        border-radius: 23px;

        .AItext {
          background: linear-gradient(19.78730805298474deg, #4446FF 0%, #4173FF 26%, #3EB8FF 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          background-clip: text;
          color: transparent;
        }
      }
    }

    .goBottom {
      width: 40px;
      height: 40px;
      position: absolute;
      right: 40px;
      top: -140px;
    }
  }
}

.logoStyle {
  width: 100%;
  // margin-top: 100px;

  img {
    height: 65%;
    width: 460px;
    margin-left: 70px;
    object-fit: contain;
    object-position: center bottom;
    vertical-align: bottom;
  }
}



// textarea的属性
:deep(.ant-input-affix-wrapper .ant-input) {
  font-size: 16px;
  height: 80px !important;
  background-color: transparent !important
}

:deep(.ant-input-textarea-clear-icon) {
  top: 15px;
}

.ant-modal-content {
  border-radius: 8px !important;
}

:deep(.ant-modal-content) {
  border-radius: 10px;
}

.loading-spinner {
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-left-color: #007bff;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.custom-popover.ant-popover-inner {
  border-radius: 15px;
}

.navigation {
  height: 100%;
  background-color: #fff;
  box-shadow: 0px 0px 24px 0px rgba(116, 157, 219, 0.24);
  // overflow: hidden;
  transition: 0.5s;

  .newChat {
    // width: 102px;
    height: 40px;
    background: linear-gradient(270deg, #0142FD 0%, #2475F9 100%);
    border-radius: 4px 4px 4px 4px;
    font-weight: 500;
    font-size: 14px;
    color: #fff;
    transition: 0.5s;
    overflow: hidden;
  }

  .width0 {
    width: 0px;
  }

  .width102 {
    width: 102px;
  }

  .allHistoryList {
    height: calc(100vh - 60px - 40px - 32px - 40px);
    padding: 0px 24px;
    overflow-y: auto;

    .title {
      font-weight: 500;
      color: rgba(0, 0, 0, 0.45);
      font-size: 14px;
    }

    .list {
      .eachChat {
        font-weight: 400;
        color: rgba(0, 0, 0, 0.85);
        font-size: 14px;
        font-family: Source Han Sans CN, Source Han Sans CN;
        transition: all 0.3s ease;
        width: 0;
        position: relative;
        border-radius: 4px;
        padding: 4px 8px;

        &:hover {
          background-color: rgba(24, 144, 255, 0.1);

          .delete-icon {
            opacity: 1;
          }
        }

        .delete-icon {
          opacity: 0;
          transition: opacity 0.3s ease;
          cursor: pointer;
          margin-right: 8px;
        }
      }

      .width0 {
        width: 0px;
      }

      .width220 {
        width: 220px;
      }
    }
  }
}

.navigation-close {
  width: 64px;
}

.navigation-open {
  width: 250px;
}

.menu-icon {
  color: #999;
  font-size: 20px;
  padding: 0 8px;
  transition: all 0.3s ease;

  &:hover {
    color: #333;
  }
}

:deep(.ant-dropdown-menu) {
  padding: 8px;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

:deep(.ant-dropdown-menu-item) {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  cursor: pointer;

  &:hover {
    background-color: #f5f5f5;
  }
}

.thinking {
  .thinkingBtnOpen {
    color: rgba(36, 69, 106, 0.65);
    font-weight: bold;
    font-size: 14px;
  }

}

.dataScreen {
  width: 900px;
  height: 168px;
  background-image: url(../../../assets/images/AI/dataScreenBg.png);
  background-size: 100% 100%;
  margin: 24px auto;
  padding: 0px 30px;

  .eachData {
    height: 75%;


    .eachDataTitle {
      font-size: 16px;
      color: rgba(0, 0, 0, 0.65);
      font-weight: 400;
    }
  }
}
</style>