<template>
  <div class="flex just-center">
    <div class="tab" v-if="showAbility">
      <span :class="['ability', { active: activeTab == '1' }]" @click="changeTab('1')">自有能力/方案能力/标准产品</span>
      <!-- <span
      :class="['scence', { active: activeTab == '2' }]"
      @click="changeTab('2')"
      >方案能力</span
    > -->
    </div>
  </div>

  <div class="searchInfo">
    <img style="width: 54px;height: 54px; position: absolute;z-index: 100;top: 14px;left: 18px;"
      src="../../../../assets/images/AI/ai.png" alt="">
    <div class="vocationPull">
      <a-config-provider :locale="zhCN" :getPopupContainer="(triggerNode) => triggerNode.parentNode">
        <a-input-search v-model:value="name" placeholder="请输入名称进行检索" @search="seekContent">
          <template #enterButton>
            <div class="flex just-center align-center">
              <img style="width: 20px;height: 20px;" class="margin_r_16" src="../../../../assets/images/AI/search.png"
                alt="">
              <div>
                搜索
              </div>
            </div>
          </template>
        </a-input-search>
      </a-config-provider>
    </div>
  </div>

  <div class="tabContent">
    <div v-if="tableList && tableList.length > 0" style="width: 100%">
      <div class="cardContent">
        <div class="card_total">
          <template v-for="(item, index) in tableList" :key="index">
            <div :class="[
              'card_content',
              {
                cardActive: cardActive == index,
                rightActive: index % 2 != 0,
                cardObvious: index < 2 && tableList.length < 3,
                bottomLine:
                  (index == tableList.length - 1 ||
                    index == tableList.length - 2) &&
                  index > 1,
              },
            ]" @mouseenter="contentColor(index)" @mouseleave="contentLeave" @click="proDetail(item)">
              <button @click="deleteBtn" class="cart-button" v-if="item.addGroup">
                <span class="add" style="color: rgba(0, 0, 0, 0.9)">
                  &nbsp;已加入</span>
              </button>
              <button @click="deleteBtn" class="cart-button" v-if="
                selectIds.includes(item.id) ||
                selectAbilityIds.includes(item.id) ||
                selectProductIds.includes(item.id) ||
                selectCaseIds.includes(item.id)
              ">
                <span class="add" style="color: rgba(0, 0, 0, 0.9)">
                  &nbsp;已选择</span>
              </button>

              <button class="cart-button pointer" @click.stop="add(item)" v-if="
                !item.addGroup &&
                !selectIds.includes(item.id) &&
                !selectAbilityIds.includes(item.id) &&
                !selectProductIds.includes(item.id) &&
                !selectCaseIds.includes(item.id)
              ">
                <img class="add-icon" src=" @/assets/images/AI/isadded.png" /><span class="add"> &nbsp;加入组合</span>
              </button>
              <div style="display: flex; margin: 24px;">
                <div style="position: relative;">
                  <div style="position: absolute;top: -10px;left: -10px;z-index: 1000;">
                    <img v-if="item.classify == 2" src="@/assets/images/newProject/classify1.png" alt="" />
                    <img v-if="item.classify == 1" src="@/assets/images/newProject/classify2.png" alt="" />
                    <img v-if="item.classify == 0" style="width: 60px;height: 32px;"
                      src="@/assets/images/AI/productTip.png" alt="" />
                  </div>
                  <a-image :width="168" :height="105" :preview="false" v-if="item.picture" :src="`${item.picture.split(',')[0]}`" />
                  <img src="@/assets/images/home/<USER>" style="width: 168px; height: 105px" v-else />
                </div>
                <div class="card_center">
                  <div class="card_text">
                    <div class="card_tag">
                      <div class="card_title" v-if="item.projectName != null">
                        {{ item.projectName }}
                      </div>
                      <div class="card_title" v-else>
                        {{ item.name }}
                      </div>

                      <!-- <span
                        class="cardTag"
                        style="background-color: #d7e6ff; color: #2e7fff"
                        >{{ item.categoryName }}</span
                      > -->
                      <span class="cityStyle" v-if="item.provider">{{
                        item.provider
                      }}</span>
                    </div>
                  </div>
                  <div class="card_des" v-if="item.projectIntroduction != null">
                    {{ item.projectIntroduction }}
                  </div>
                  <div class="card_des" v-else>
                    {{ item.summary }}
                  </div>
                  <div style="
                      display: flex;
                      align-items: center;
                      justify-content: space-between;
                    ">
                    <div style="
                        display: flex;
                        align-items: center;
                        justify-content: space-between;
                      "></div>
                  </div>
                  <div style="
                      display: flex;
                      align-items: center;
                      justify-content: space-between;
                    ">
                    <div>
                      <!-- <img
                        style="width: 112px; height: 22px"
                        src="@/assets/images/home/<USER>"
                      /> -->
                    </div>
                    <div style="display: flex; align-items: center">
                      <img src="@/assets/images/home/<USER>" style="width: 16px; height: 16px" />
                      <span style="font-size: 12px; color: rgba(0, 0, 0, 0.45)" v-if="item.viewCount != null">{{
                        item.viewCount }}</span>
                      <span v-else>-</span>
                      <img src="@/assets/images/home/<USER>"
                        style="width: 16px; height: 16px; margin-left: 18px" />
                      <span style="font-size: 12px; color: rgba(0, 0, 0, 0.45)" v-if="item.downloadCount != null">{{
                        item.downloadCount }}</span>
                      <span v-else>-</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </template>
        </div>
      </div>
      <div class="layPage">
        <a-pagination v-model:pageSize="pageItemSize" v-model:current="currentPage" :pageSizeOptions="pageSizeOptions"
          show-quick-jumper show-size-changer :total="totalItemCount" @change="pageChange" @showSizeChange="sizeChange"
          class="mypage" />
      </div>
      <div class="btn_box">
        <span class="refuse" @click="refuse">取消</span>
        <button :class="{
          submit: selectNum > 0,
          disabled: selectNum == 0,
        }" @click="submit" :disabled="selectNum == 0">
          确认添加（已选择{{ selectNum }}个）
        </button>
      </div>
    </div>
    <div v-if="tableList && tableList.length == 0" class="emptyPhoto">
      <img src="@/assets/images/home/<USER>" />
    </div>
    <div class="loading" v-show="loadingShow">
      <a-spin />
    </div>
  </div>
</template>

<script>
import { defineComponent, reactive, toRefs, ref, watch } from "vue";
import { useRouter } from "vue-router";
import { getSceneSchemeList, getProjectList, getNewCaseList } from "@/api/moduleList/home";
import zhCN from "ant-design-vue/es/locale/zh_CN";
import { addCombine } from "@/api/combine/combine.js";
import { guideList } from "@/api/moduleList/home";
import eventBus from "@/utils/eventBus";
import { getSeparateAIList } from "@/api/AI/ai.js"
import { select } from "ranui";
import { dataType } from "element-plus/es/components/table-v2/src/common.mjs";
import { AISearch } from "../../../../api/AI/ai";
export default defineComponent({
  components: {},
  props: {
    choseType: {
      type: Number,
      default: null,
    },
    labelId: {
      type: Number,
      default: null,
    },
    categoryId: {
      type: Number,
      default: null,
    },
    showAbility: {
      type: Boolean,
      default: false,
    },
    cover: {
      type: String,
      default: "",
    },
    conclusion: {
      type: String,
      default: "",
    },
    nowCustomizeSolutionId: {
      type: Number,
      default: null,
    }
  },
  setup(props, { emit }) {
    const vocation = ref("");
    const region = ref("");
    const data = reactive({
      localStorageGetSolutionId: null,
      nowCustomizeSolutionId: props.nowCustomizeSolutionId,
      choseType: props.choseType,
      showAbility: props.showAbility,
      labelId: props.labelId,
      categoryId: props.categoryId,
      name: "",
      moment: "",
      loadingShow: true,
      activeTab: props.showAbility ? "1" : "",
      activeKey: "",
      cardActive: "-1",
      pageSizeOptions: ["10", "20", "30", "50"],
      totalItemCount: 0,
      tableList: [],
      totalNum: 0,
      totalItemCount1: 0,
      currentPage: 1,
      pageItemSize: 10,
      selectIds: [],
      selectAbilityIds: [],
      selectProductIds: [],
      selectCaseIds: [],
      selectNum: 0,
      isAISearch: false,
      AISearchList: [],
      AISearchSolutionList: [],
      searchType: props.showAbility ? 3 : null,
    });
    const getList = () => {
      let pageParams = {
        pageNo: data.currentPage,
        pageSize: data.pageItemSize,
        keyword: data.name,
        type: data.choseType,
        isSensitive: 0,
      };
      data.loadingShow = true;
      if (data.choseType == 5) {
        // 点击添加应用场景（能力/方案/产品）
        if (data.name) {
          data.isAISearch = true
          // 调AI单召回接口 方案type=2，能力type=3，产品type=6
          let params = {
            question: data.name,
            // type: data.searchType,//能力方案产品三合一不传type
            busiId: data.localStorageGetSolutionId,
            resultType: 4,
          }
          getSeparateAIList(params).then((res) => {
            console.log('res', res)
            data.loadingShow = false;
            data.AISearchList = [];
            data.AISearchList = res.data;
            data.totalItemCount = res.data.length;
            data.AISearchList.map((item) => {
              item.summary = item.introduce;
            });
            data.tableList = data.AISearchList.slice(0, 10)
            if (data.activeKey == "") {
              data.totalItemCount1 = res.data.totalRows;
            }
          })
        } else {
          data.isAISearch = false
          // pageParams.labelId = data.categoryId;
          guideList(pageParams)
            .then((res) => {
              data.loadingShow = false;
              data.tableList = [];
              data.tableList = res.data.rows;
              data.totalItemCount = res.data.totalRows;
              data.tableList.map((item) => {
                item.summary = item.introduce;
              });

              if (data.activeKey == "") {
                data.totalItemCount1 = res.data.totalRows;
              }
            })
            .catch((err) => {
              data.loadingShow = false;
            });
        }

      } else {
        if (data.name) {
          // 调AI单召回接口 应用案例type=7
          data.isAISearch = true
          let params = {
            question: data.name,
            type: 7,
            busiId: data.localStorageGetSolutionId,
            resultType: 4,
          }
          getSeparateAIList(params).then((res) => {
            console.log('res', res)
            data.loadingShow = false;
            data.tableList = [];
            data.tableList = res.data;
            data.totalItemCount = res.data.length;
            data.tableList.map((item) => {
              item.picture = item.imageUrl;
              item.summary = item.constructionContent;
              item.name = item.caseName;
            });

            if (data.activeKey == "") {
              data.totalItemCount1 = res.data.totalRows;
            }
          })
        } else {
          data.isAISearch = false
          if (data.choseType == 8) {
            // 点击添加应用案例
            pageParams.industryId = data.labelId || localStorage.getItem("industryId");
            pageParams.addGroup = false;
            pageParams.isDecoupling = 1;
            pageParams.shelfStatus = 1;
            pageParams.caseType = 1;
          }
          
          getNewCaseList(pageParams)
          // getSceneSchemeList(pageParams)
            .then((res) => {
              data.loadingShow = false;
              data.tableList = [];
              data.tableList = res.data.rows;
              data.totalItemCount = res.data.totalRows;
              data.tableList.map((item) => {
                item.picture = item.imageUrl;
                item.summary = item.constructionContent;
                item.name = item.caseName;
              });

              if (data.activeKey == "") {
                data.totalItemCount1 = res.data.totalRows;
              }
            })
            .catch((error) => {
              data.loadingShow = false;
            });
        }
      }
    };
    getList();
    eventBus.on("moduleRefresh", getList);
    const seekContent = () => {
      data.currentPage = 1;
      getList();
    };

    const contentColor = (index) => {
      data.cardActive = index;
    };
    const router = useRouter();
    const proDetail = (val) => {
      console.log(val);
      if (val.classify == 0) {
        // 产品
        window.open(window.location.origin + "/#/product/productDetail?id=" + val.id);
      } else if (val.classify == 1) {
        // 能力
        window.open(window.location.origin + "/#/module/modulelNew?id=" + val.id);
      } else if (val.classify == 2) {
        // 方案场景
        window.open(window.location.origin + "/#/sceneSolution/applyNew?id=" + val.id);
      }
      if (val.type == 8) {
        window.open(window.location.origin + "/#/caseStore/detailNew?id=" + val.id);
      }
    };
    const contentLeave = () => {
      data.cardActive = "-1";
    };

    const add = (item) => {
    	if (data.choseType == 5) {
	      if (item.classify == 1) {
	        data.selectAbilityIds.push(item.id);
	      } else if (item.classify == 0) {
	        data.selectProductIds.push(item.id);
	      } else {
	        data.selectIds.push(item.id);
	      }
	      console.log('data.selectAbilityIds', data.selectAbilityIds)
	      console.log('data.selectProductIds', data.selectProductIds)
	      console.log('data.selectIds', data.selectIds)
	      data.selectNum = data.selectAbilityIds.length + data.selectIds.length + data.selectProductIds.length;
	      // getList();
      } else if (data.choseType == 8) {
      	data.selectCaseIds.push(item.id);
      	data.selectNum = data.selectCaseIds.length;
      }
    };
    const refuse = () => {
      data.selectIds = [];
      data.selectAbilityIds = [];
      data.selectProductIds = [];
      data.selectCaseIds = [];
      emit("close");
    };
    const submit = () => {
      const schemes = data.selectIds.map((item) => {
        return {
          schemeId: item,
          classify: "2",
          cover: props.cover,
          conclusion: props.conclusion,
        };
      });
      const scheme = data.selectAbilityIds.map((item) => {
        return {
          schemeId: item,
          classify: "1",
          cover: props.cover,
          conclusion: props.conclusion,
        };
      });
      const productScheme = data.selectProductIds.map((item) => {
        return {
          schemeId: item,
          classify: "0",
          cover: props.cover,
          conclusion: props.conclusion,
        };
      });
      const caseScheme = data.selectCaseIds.map((item) => {
        return {
          schemeId: item,
          classify: "6",
          cover: props.cover,
          conclusion: props.conclusion,
        };
      });
      data.selectIds = [];
      data.selectAbilityIds = [];
      data.selectProductIds = [];
      data.selectCaseIds = [];
      schemes.push(...scheme,...productScheme,...caseScheme);
      addCombine(schemes).then((res) => {
        emit("close");
      });
    };
    const pageChange = (page, pageSize) => {
      data.currentPage = page;
      if (!data.isAISearch) {
        getList();
      } else {
        // 前端手动分页
        data.tableList = data.AISearchList.slice((data.currentPage - 1) * data.pageItemSize, data.currentPage * data.pageItemSize)
      }
    };
    const sizeChange = (current, size) => {
      data.pageItemSize = size;
      if (!data.isAISearch) {
        getList();
      } else {
        // 前端手动分页
        data.tableList = data.AISearchList.slice((data.currentPage - 1) * data.pageItemSize, data.currentPage * data.pageItemSize)
      }
    };
    const deleteBtn = (id) => {
      const index = data.selectIds.indexOf(id);
      if (index > -1) {
        data.selectIds.splice(index, 1);
      }
    };
    watch(
      () => props.choseType,
      (newV) => {
        data.choseType = newV;
      }
    );
    watch(
      () => props.nowCustomizeSolutionId,
      (newV) => {
        data.nowCustomizeSolutionId = newV;
      }
    );
    const changeTab = (val) => {
      data.activeTab = val;
      data.currentPage = 1;
      getList();
    };
    const getlocalStorageId = () => {
      data.localStorageGetSolutionId = localStorage.getItem("AInowCustomizeSolutionId")
      console.log('data.localStorageGetSolutionId', data.localStorageGetSolutionId)
    }
    getlocalStorageId()
    return {
      ...toRefs(data),
      vocation,
      changeTab,
      region,
      add,
      contentColor,
      sizeChange,
      contentLeave,
      proDetail,
      router,
      pageChange,
      zhCN,
      deleteBtn,
      refuse,
      seekContent,
      submit,
    };
  },
});
</script>

<style lang="scss" scoped src="./choseTable.scss"></style>

<style lang="scss">
.mypage {
  .ant-pagination {
    color: rgba(0, 0, 0, 0.65);
  }

  .ant-select-selector {
    color: rgba(0, 0, 0, 0.65);
  }

  .ant-pagination-item-active {
    background: #007eff;
  }

  .ant-pagination-item-active a {
    color: #ffffff;
  }

  .ant-pagination-item-active:focus a,
  .ant-pagination-item-active:hover a {
    color: #ffffff;
  }

  .ant-pagination-item-active a:focus,
  .ant-pagination-item-active a:hover {
    color: #ffffff;
  }

  .ant-pagination-item:focus,
  .ant-pagination-item:hover {
    background: #007eff;
  }

  .ant-pagination-item:focus a,
  .ant-pagination-item:hover a {
    color: #ffffff;
  }

  .ant-pagination-item-disabled:hover,
  .ant-pagination-item-disabled:focus {
    background-color: #ffffff;
    border-color: #d9d9d9;

    a {
      color: rgba(0, 0, 0, 0.25);
    }
  }

  .ant-pagination-options-size-changer.ant-select {
    margin-right: 0;
  }

  .ant-pagination-options-quick-jumper {
    margin-left: 8px;
  }
}

/*.shopping {
  position: absolute;
  right: 4px;
  bottom: 12px;
}*/
</style>