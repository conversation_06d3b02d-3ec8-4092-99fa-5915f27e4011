<template>
  <div class="searchInfo flex" :class="switchOnOff ? contentType == 'K' ? 'AIBg' : 'AIBgShengTai' : 'commonBg'">
    <div style="min-height: 56px;background: #ffffff;justify-content: center;align-items: center;" class=""></div>
    <div class="vocationPull" style="flex: 1; height: 56px">
      <div class="switch">
        <div class="AIlogo"></div>
        <a-switch checked-children="on" un-checked-children="off" v-model:checked="switchOnOff" />
      </div>
      <a-config-provider :locale="zhCN" :getPopupContainer="(triggerNode) => triggerNode.parentNode">
        <a-input v-model:value="name" class="inputClass" allow-clear @keyup.enter="seekContent"
          :placeholder="switchOnOff ? contentType == 'K' ? '请输入能力名称、标签等关键词进行搜索' : '请输入生态公司名称进行查询，或以“xx生态的xx能力”的形式进行指定生态的内容搜索' : '请输入能力名称、标签等关键词进行搜索'" />
        <!--<voiceRecorder
          v-if="switchOnOff"
          :isTranslating="isTranslating"
          :canBtnUse="canBtnUse"
          @audioReady="handleAudio"
        />-->
        <div class="flex align-center">
          <div v-if="switchOnOff" class="chooseSearchType flex align-center ">
            <div class="chooseContent flex align-center just-sb"
              :class="contentType === 'K' ? 'border_blue' : 'border_green'">
              <div class="solutionBox flex align-center just-center pointer" :class="[
                contentType === 'K' ? 'solutionBg' : '',
                contentType === 'K' ? 'choosedWord' : 'nochoosedWord'
              ]" @click="chooseContentType('K')">
                <div class="flex align-center just-center">
                  <div class="margin_r_4">
                    <img v-if="contentType === 'K'" src="../../../../../assets/images/AI/solutionIcon2.png" alt="">
                    <img v-else src="../../../../../assets/images/AI/solutionIcon1.png" alt="">
                  </div>
                  <div>能力</div>
                </div>
              </div>
              <div class="shengtaiBox flex align-center just-center pointer" :class="[
                contentType === 'E' ? 'shengtaiBg' : '',
                contentType === 'E' ? 'choosedWord' : 'nochoosedWord'
              ]" @click="chooseContentType('E')">
                <div class="flex align-center just-center">
                  <div class="margin_r_4">
                    <img v-if="contentType === 'E'" src="../../../../../assets/images/AI/shengtai2.png" alt="">
                    <img v-else src="../../../../../assets/images/AI/shengtai1.png" alt="">
                  </div>
                  <div>生态</div>
                </div>
              </div>
            </div>
          </div>
          <div style="position: relative;">
            <div class="seekInfo border_top_right_5" :class="[
              switchOnOff ? contentType == 'K' ? 'AIbtn' : 'AIbtnShengTai' : 'commonBtn'
            ]" @click="seekContent()">
              <img src="@/assets/images/home/<USER>" />
              <div>搜索</div>
            </div>
          </div>
        </div>
      </a-config-provider>
    </div>
  </div>
  <div class="newLoading" v-if="AIsearch">
    <loadingSmall />
  </div>
  <div class="selectData" v-if="true || !switchOnOff">
    <!-- <div class="selcet_box">
      <div class="left_select">能力种类：</div>
      <div class="right_select" id="getWidth">
        <span
          v-for="(item, key) in abilList"
          :class="{ activeBtn: activeLabel == item.name }"
          :key="key"
          @click="abilityBtn(item)"
        >
          {{ item.name }}
        </span>
      </div>
    </div> -->
    <!-- <div :class="['selcet_box', { showMore: showScense == 'provider' }]">
      <div class="left_select">能力分类：</div>
      <div class="right_select" id="getWidth">
        <span
          v-for="(item, key) in tabList"
          :class="{ activeBtn: activeKey === item.value }"
          :key="key"
          @click="providerBtn(item)"
        >
          {{ item.label }}
        </span>
      </div>
      <span
        class="more flex"
        v-if="tabList.length > 8 && morePro"
        @click="showMore('provider')"
        >更多 <img src="@/assets/images/solution/home/<USER>" alt=""
      /></span>
      <span
        class="more flex"
        v-if="tabList.length > 8 && !morePro"
        @click="showless('provider_less')"
        >收起 <img src="@/assets/images/solution/home/<USER>" alt=""
      /></span>
    </div>
    <div v-if="showLabel" class="second_line">
      <span @click="toggleShowLabel">展开</span>
      <div class="img_box">
        <img src="@/assets/images/solution/home/<USER>" alt="" class="first" />
        <img src="@/assets/images/solution/home/<USER>" alt="" class="sec" />
      </div>
    </div> -->

    <div :class="['selcet_box', { showMore: showScense == 'label' && showIndex === index }]"
      v-for="(val, index) in labelList" :key="index">
      <div class="left_select">{{ val.label }}：</div>
      <div :class="['right_select', { showHidden: showScense == 'label' && showIndex === index }]">
        <span v-if="val.label == '提供方'" v-for="(value, key1) in val.children" :key="key1"
          :class="{ activeBtn: providerName === value.label }" @click="providerClick(value, index)">
          <div class="title">{{ value.label }}</div>
        </span>
        <span v-else v-for="(value, key) in val.children" :key="key"
          :class="{ activeBtn: selectList.indexOf(value.label) > -1 }" @click="labelSelect(value, 'default', index)">
          <div class="title">{{ value.label }}</div>
        </span>
      </div>
      <span class="more flex" v-if="val.children && val.children.length > 8 && showIndex !== index"
        @click="showMore('label', index)">更多<img src="@/assets/images/solution/home/<USER>" alt="" /></span>
      <span class="more flex" v-if="val.children && val.children.length > 8 && showIndex === index"
        @click="showless('label_less', index)">收起<img src="@/assets/images/solution/home/<USER>" alt="" /></span>
    </div>
    <div :class="['selcet_box', { showMore: showScense == 'provider' && showIndex === index }]">
      <div class="left_select">交付方式：</div>
      <div class="right_select" id="getWidth">
        <span v-for="(item, key) in tabList" :class="{ activeBtn: deliverList.indexOf(item.value) > -1 }" :key="key"
          @click="providerBtn(item)">
          <div class="title">{{ item.value }}</div>
        </span>
      </div>
      <span class="more flex" v-if="tabList.length > 8 && morePro" @click="showMore('provider')">更多 <img
          src="@/assets/images/solution/home/<USER>" alt="" /></span>
      <span class="more flex" v-if="tabList.length > 8 && !morePro" @click="showless('provider_less')">收起 <img
          src="@/assets/images/solution/home/<USER>" alt="" /></span>
    </div>
    <!-- <div v-if="!showLabel" class="second_line">
      <span @click="toggleShowLabel">收起</span>
      <div class="img_box">
        <img src="@/assets/images/solution/home/<USER>" class="first" alt="" />
        <img src="@/assets/images/solution/home/<USER>" class="sec" alt="" />
      </div>
    </div> -->

    <div class="selectData" style="z-index: 2000;" v-if="contentType == 'K' || (contentType != 'K' && !switchOnOff)">
      <div :class="['selcet_box']">
        <div class="left_select">{{ '生态合作方' }}：</div>
        <div :class="['right_select']">
          <a-input v-model:value="searchContent.label" placeholder="请输入生态合作方名称" allowClear @change="searchChange"
            @click="clickOn" @blur="clickBlur" />
          <!-- searchShow && searchList.length > 0 -->
        </div>
      </div>
      <div class="searchList" v-if="searchShow && searchList && searchList.length > 0">
        <p v-for="(item, index) in searchList" :key="index" @mousedown="setName(item)">
          {{ item.name }}
        </p>
      </div>
    </div>

    <div class="select_boot flex">
      <div>
        已选条件：
        <span v-for="(val, index) in activeLabelList" :key="index">{{
          val
        }}</span><img src="@/assets/images/solution/home/<USER>" alt="" v-if="activeLabelList.length > 0"
          style="width: 16px; height: 16px; cursor: pointer" @click="deleteSelect(val, index, 'label')" />
        <!-- <span v-for="(val, index) in providerSelect" :key="index">{{
          val
        }}</span
        ><img
          src="@/assets/images/solution/home/<USER>"
          alt=""
          v-if="providerSelect.length > 0"
          style="width: 16px; height: 16px; cursor: pointer"
          @click="deleteSelect(val, index, 'pro')"
        /> -->
        <span v-for="(val, index) in abilitySelect" :key="index">{{ val }}</span><img
          src="@/assets/images/solution/home/<USER>" alt="" v-if="abilitySelect.length > 0"
          style="width: 16px; height: 16px; cursor: pointer" @click="deleteSelect(val, index, 'type')" />
        <span class="label" v-if="selectList.length > 0">能力：</span>
        <span v-for="(item, index) in selectList" :key="key">
          <span style="margin-left: 8px">
            {{ item }}
          </span>
          <img src="@/assets/images/solution/home/<USER>" alt="" style="width: 16px; height: 16px; cursor: pointer"
            @click="deleteSelect(item, index)" />
        </span>
        <span class="label" v-if="providerSelect.length > 0">交付方式：</span>
        <span v-for="(item, index) in providerSelect" :key="key">
          <span style="margin-left: 8px">
            {{ item }}
          </span>
          <img src="@/assets/images/solution/home/<USER>" alt="" style="width: 16px; height: 16px; cursor: pointer"
            @click="deleteSelect(item, index, 'pay')" />
        </span>
        <span v-if="providerName !== ''">提供方：</span>
        <span v-if="providerName !== ''">
          <span style="margin-left: 8px">
            {{ providerName }}
          </span>
          <img src="@/assets/images/solution/home/<USER>" alt="" style="width: 16px; height: 16px; cursor: pointer"
            @click="deleteSelect(providerName, 0, 'provider')" />
        </span>
      </div>
      <div class="right_con">
        共找到 <span>{{ totalItemCount }}</span> 条结果
      </div>
    </div>
  </div>

  <div class="tabContent">
    <div v-if="tableList && tableList.length > 0" style="width: 100%">
      <div class="AITips flex align-center" v-if="!showPagination">
        <img style="width: 40px; height: 40px; margin-right: 10px" src="../../../../../assets/images/AI/ai.png"
          alt="" />
        <div class="words">以下是AI助手为您找到的相关结果</div>
      </div>
      <div class="cardContent">
        <div class="card_total flex-1">
          <template v-for="(item, index) in tableList" :key="index">
            <div :class="[
              'card_content',
              {
                cardActive: cardActive == index,
                rightActive: index % 2 != 0,
                cardObvious: index < 2 && tableList.length < 3,
                bottomLine:
                  (index == tableList.length - 1 ||
                    index == tableList.length - 2) &&
                  index > 1,
              },
            ]" @mouseenter="contentColor(index)" @mouseleave="contentLeave" @click="proDetail(item)">
              <div class="listTag">
                <!--<img v-if="item.isEcologyOrAbility==1" src="@/assets/images/newProject/classify6.png" alt="" />-->
                <img v-if="item.ecologyType && item.ecologyType.split(',').includes('1')"
                  src="@/assets/images/newProject/classify7.png" alt="" />
              </div>
              <div style="display: flex; margin: 24px">
                <div>
                  <img :width="168" :height="105" v-if="item.abilityPicture" v-lazy="`${item.abilityPicture}`" />
                  <img src="@/assets/images/home/<USER>" style="width: 168px; height: 105px" v-else />
                </div>
                <div class="card_center">
                  <div class="card_text">
                    <div class="card_tag">
                      <a-tag color="#D7E6FF">{{ item.abilityType }}</a-tag>
                      <div class="card_title">{{ item.name }}</div>
                    </div>
                    <div class="flex" v-if="item.provider">
                      <a-tag :bordered="false" class="cityStyle" style="margin-right: 0">{{ item.provider }}</a-tag>
                    </div>
                  </div>
                  <div class="card_des">
                    {{ item.abilityIntro }}
                  </div>
                  <div class="flex" style="justify-content: space-between">
                    <div class="flex">
                      <!-- <a-tag
                        color="#D7E6FF"
                        style="
                          display: block;
                          color: rgba(0, 0, 0, 0.45);
                          background-color: transparent;
                          border: 1px solid #d9d9d9;
                          line-height: 17px;
                        "
                        >{{ item.label[0] }}</a-tag
                      > -->
                      <!-- <a-tag
                        color="#D7E6FF"
                        v-if="item.label[1]"
                        style="
                          display: block;
                          color: rgba(0, 0, 0, 0.45);
                          background-color: transparent;
                          border: 1px solid #d9d9d9;
                          line-height: 17px;
                        "
                        >{{ item.label[1] }}</a-tag
                      > -->
                    </div>
                  </div>

                  <div style="
                      display: flex;
                      align-items: center;
                      justify-content: space-between;
                      padding-right: 6px;
                    ">
                    <!-- <div class="flex">
                      <img
                        style="width: 112px; height: 22px"
                        src="@/assets/images/home/<USER>"
                      />
                    </div> -->
                    <div style="display: flex; align-items: center">
                      <img src="@/assets/images/home/<USER>" style="width: 16px; height: 16px" />
                      <span style="font-size: 12px; color: rgba(0, 0, 0, 0.45)" v-if="item.viewCount">{{ item.viewCount
                      }}</span>
                      <span v-else>-</span>
                      <img src="@/assets/images/home/<USER>"
                        style="width: 16px; height: 16px; margin-left: 18px" />
                      <span style="font-size: 12px; color: rgba(0, 0, 0, 0.45)" v-if="item.downloadCount">{{
                        item.downloadCount }}</span>
                      <span v-else>-</span>
                    </div>
                    <div>
                      <div v-if="item.addCart">
                        <button disabled class="cart-button">
                          <span class="add" style="color: rgba(0, 0, 0, 0.9)">
                            &nbsp;已加入</span>
                        </button>
                      </div>
                      <div v-else>
                        <button class="cart-button pointer" @click.stop="add(item.id)">
                          <img class="add-icon" src=" @/assets/images/AI/isadded.png" /><span class="add">
                            &nbsp;加入预选</span>
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </template>
        </div>
      </div>
      <div class="layPage">
        <a-pagination v-model:pageSize="pageItemSize" v-model:current="currentPage" :pageSizeOptions="pageSizeOptions"
          show-quick-jumper show-size-changer :total="totalItemCount" @change="pageChange" @showSizeChange="sizeChange"
          class="mypage" />
      </div>
    </div>
    <div v-if="tableList && tableList.length == 0" class="emptyPhoto">
      <img src="@/assets/images/home/<USER>" />
    </div>
    <div class="loading" v-show="loadingShow">
      <a-spin />
    </div>
  </div>
</template>

<script>
import { defineComponent, reactive, toRefs, ref, onMounted } from "vue";
import { useRouter } from "vue-router";

import {
  getLabelTreeList,
  getTypeList,
  getTradeList,
  getTeamSchemeTree,
  getSearchList
} from "@/api/solutionNew/home";

import { getProjectList, selectLabel } from "@/api/moduleList/home";
import { AISearch, AIvoice, getNewAssociate } from "@/api/AI/ai.js";
import voiceRecorder from "@/components/voiceRecorder/voiceRecorder.vue";
import zhCN from "ant-design-vue/es/locale/zh_CN";

import loadingSmall from "@/components/superLoadingSmall/loadingSmall.vue";

import { addShoppingCart } from "@/api/combine/shoppingCart.js";
import { getMakeUrl } from "@/utils/getUrl";
import eventBus from "@/utils/eventBus";
import { DownOutlined } from '@ant-design/icons-vue';
let recorder; // 定义全局变量
let audioChunks = [];
export default defineComponent({
  components: {
    voiceRecorder,
    loadingSmall,
    DownOutlined,
  },
  setup() {
    // const recorder = undefined;
    const baseURL = getMakeUrl();
    const vocation = ref("5");
    const region = ref("");
    const searchList = ref([])
    const searchShow = ref(false)
    const data = reactive({
      contentNear: '检索相近内容',
      searchContent: {
        label: '',
        id: ''
      },
      name: "",
      providerName: "",
      AIsearch: false,
      moment: "",
      loadingShow: true,
      activeKey: "",
      activeKeyAbility: "",
      cardActive: "-1",
      pageSizeOptions: ["10", "20", "30", "50"],
      totalItemCount: 0,
      tabList: [
        { key: "0", value: "API" },
        { key: "1", value: "SDK类" },
        { key: "2", value: "私有化部署类" },
      ],
      isRecording: false,
      labelIdlist: [],
      labelIdlist1: undefined,
      vocationList: [],
      labelList: [
        {
          value: "通用能力",
          label: "通用能力",
          children: [
            { label: "网络", value: "网络" },
            { label: "通讯", value: "通讯" },
            { label: "物联网", value: "物联网" },
            { label: "大数据", value: "大数据" },
            { label: "AI", value: "AI" },
            { label: "安全", value: "安全" },
            { label: "视频", value: "视频" },
            { label: "位置/地图", value: "位置/地图" },
            { label: "认证", value: "认证" },
            { label: "办公", value: "办公" },
            { label: "双碳", value: "双碳" },
            { label: "流程自动化", value: "流程自动化" },
            { label: "区块链", value: "区块链" },
            { label: "运维", value: "运维" },
            { label: "无人机", value: "无人机" },
          ],
        },
        {
          value: "行业能力",
          label: "行业能力",
          children: [
            { label: "党政", value: "党政" },
            { label: "金融", value: "金融" },
            { label: "交通", value: "交通" },
            { label: "教育", value: "教育" },
            { label: "工业", value: "工业" },
            { label: "医疗", value: "医疗" },
            { label: "互联网", value: "互联网" },
            { label: "农业", value: "农业" },
            { label: "建筑", value: "建筑" },
            { label: "文旅", value: "文旅" },
            { label: "商客", value: "商客" },
          ],
        },
      ],
      tableList: [],
      tableAIAllList: [],
      filterArr: [],
      totalNum: 0,
      totalItemCount1: 0,
      currentPage: 1,
      pageItemSize: 10,
      showLast: false,
      showId: undefined,
      selectList: [],
      selectListNew: [],
      showMore: false,
      showScense: "",
      morePro: true,
      abilityPro: true,
      providerSelect: [],
      abilitySelect: [],
      activeLabel: "",
      activeLabelList: [],
      abilList: [
        {
          id: 1,
          name: "原子方案",
        },
        {
          id: 2,
          name: "自有能力",
        },
      ],
      showIndex: "",
      showPagination: true,
      showLabel: true,
      switchOnOff: true,
      isTranslating: false,
      canBtnUse: false,
      deliverList: [],
      contentType: "K",
      searchText: "搜索",
      showEcoSearch: false,
      showDownIcon: true,
      isHover: false,
    });
    const getList = () => {
      let pageParams = {
        pageNo: data.currentPage,
        pageSize: data.pageItemSize,
        keyword: data.name,
        // type: 5,
        source: "自有能力",
        abilityTypeIds: data.labelIdlist,
        provider: data.providerName,
        deliveryMethodStrs: data.deliverList.join(","),
        ecopartnerId: data.searchContent.id
      };
      data.loadingShow = true;
      getProjectList(pageParams)
        .then((res) => {
          data.showPagination = true;
          data.loadingShow = false;
          data.tableList = [];
          data.tableList = res.data.rows;
          data.totalItemCount = res.data.totalRows;
          data.tableList.map((item) => {
            item.provider = item.provider.split("/")[0];
          });

          if (data.activeKey == "") {
            data.totalItemCount1 = res.data.totalRows;
          }
        })
        .catch((error) => {
          data.loadingShow = false;
        });
    };
    const getLabel = () => {
      selectLabel().then((res) => {
        data.labelList = res.data.map((item) => ({
          label: item.name,
          value: item.id,
          length: item.children ? item.children.length : 0,
          children: item.children
            ? item.children.map((child) => ({
              label: child.name,
              value: child.id,
              children: child.children
                ? child.children.map((ele) => ({
                  label: ele.name,
                  value: ele.id,
                }))
                : undefined,
            }))
            : undefined,
        }));
        getTeamSchemeTree().then((resTree) => {
          console.log(resTree);
          let treeList = [];
          resTree.data.forEach(item => {
            item.label = item.name;
            item.value = item.id;
            treeList.push(item)
          })
          data.labelList.push({
            label: "提供方",
            children: treeList
          })
        })
      });
    };
    getLabel();
    const labelChange = (val) => {
      data.labelIdlist = val.join(",");
    };
    getList();
    // eventBus.on("moduleRefresh", getList);

    // 回车搜索
    const seekContent = debounce(function () {
      console.log('data.switchOnOff', data.switchOnOff)
      data.currentPage = 1;
      if (data.switchOnOff) {
        console.log('AI', data.switchOnOff)
        if(data.AIsearch){
          return
        }else{
          getAIList();
        }
      } else {
        console.log('自有能力', data.switchOnOff)
        getList();
      }
    },100);
    const changeSearch = () => {
      data.contentType = data.contentType == 'K' ? 'E' : 'K';
      data.searchText = data.contentType == 'K' ? '搜能力' : '搜生态';
      seekContent();
    }
    const regionChange = (val) => {
      region.value = val;
      getList();
    };
    const tabChange = (val) => {
      if (val.value !== data.activeKey) {
        data.activeKey = val.value;
        data.currentPage = 1;
        getList();
      }
    };
    const contentColor = (index) => {
      data.cardActive = index;
    };
    const router = useRouter();
    const proDetail = (val) => {
      router.push({
        query: {
          id: val.id,
        },
        name: "modulelNew",
      });
    };
    const contentLeave = () => {
      data.cardActive = "-1";
    };

    const add = (id) => {
      addShoppingCart({
        schemeId: id,
        type: "2",
      }).then((res) => {
        refreshList();
        eventBus.emit("cartRefresh");
      });
    };

    const pageChange = (page, pageSize) => {
      data.currentPage = page;
      if (data.showPagination) {
        getList();
      } else {
        getAIPageList();
      }
    };
    const sizeChange = (current, size) => {
      data.pageItemSize = size;
      if (data.showPagination) {
        getList();
      } else {
        getAIPageList();
      }
    };
    const getTarde = () => {
      let tradeParams = {};
      // getTradeList(tradeParams).then((result) => {
      //   result.data.map((item) => {
      //     data.tabList.push({
      //       label: item.name,
      //       value: item.id,
      //     });
      //   });
      // });

      getTypeList({
        pageNo: 1,
        pageSize: 100,
      }).then((res) => {
        data.vocationList = [];
        res.data.rows.map((item) => {
          data.vocationList.push({
            label: item.name,
            value: item.id,
          });
        });
      });
    };
    // ai搜索筛选
    const aiFilter = () => {
      if (data.providerName != '') {
        data.filterArr = data.tableAIAllList.filter(item => {
          return item.provider.includes(data.providerName)
        })
      }
      if (data.selectList.length > 0) {
        data.filterArr = data.tableAIAllList.filter(item => {
          return data.selectListNew.includes(item.abilityTypeId * 1)
        })
      }
      if (data.deliverList.length > 0) {
        data.filterArr = data.tableAIAllList.filter(item => {
          let ar = item.deliveryMethod.split(',')
          return ar.some(item1 => data.deliverList.includes(item1))
        })
      }
      if (data.providerName != '' && data.selectList.length > 0) {
        let arr = data.tableAIAllList.filter(item => {
          return item.provider.includes(data.providerName)
        })
        data.filterArr = arr.filter(item => {
          return data.selectListNew.includes(item.abilityTypeId * 1)
        })
      }
      if (data.providerName != '' && data.deliverList.length > 0) {
        let arr = data.tableAIAllList.filter(item => {
          return item.provider.includes(data.providerName)
        })
        data.filterArr = arr.filter(item => {
          let ar = item.deliveryMethod.split(',')
          return ar.some(item1 => data.deliverList.includes(item1))
        })
      }
      if (data.selectList.length > 0 && data.deliverList.length > 0) {
        let arr = data.tableAIAllList.filter(item => {
          let ar = item.deliveryMethod.split(',')
          return ar.some(item1 => data.deliverList.includes(item1))
        })
        data.filterArr = arr.filter(item => {
          return data.selectListNew.includes(item.abilityTypeId * 1)
        })
      }
      if (data.providerName != '' && data.selectList.length > 0 && data.deliverList.length > 0) {
        let arr = data.tableAIAllList.filter(item => {
          return item.provider.includes(data.providerName)
        })
        let arr1 = arr.filter(item => {
          let ar = item.deliveryMethod.split(',')
          return ar.some(item1 => data.deliverList.includes(item1))
        })
        data.filterArr = arr1.filter(item => {
          return data.selectListNew.includes(item.abilityTypeId * 1)
        })
      }
      if (data.providerName == '' && data.selectList.length == 0 && data.deliverList.length == 0) {
        data.filterArr = data.tableAIAllList
      }
      let firstPageNum = (data.currentPage - 1) * data.pageItemSize;
      let lastPageNum = data.currentPage * data.pageItemSize;
      data.tableList = data.filterArr.slice(firstPageNum, lastPageNum);
      data.totalItemCount = data.filterArr.length
    }

    const labelSelect = (value, type = "default", index) => {
      if (value.children && type !== "last") {
        data.showLast = !data.showLast;
        data.showId = value.value;
        data.showScense = "label";
        data.showIndex = index;
      } else {
        if (data.selectList.includes(value.label)) {
          const index = data.selectList.findIndex(
            (item) => item === value.label
          );
          if (index !== -1) {
            data.selectList.splice(index, 1);
          }
          const index1 = data.selectListNew.findIndex(
            (item) => item === value.value
          );
          if (index1 !== -1) {
            data.selectListNew.splice(index, 1);
          }
        } else {
          data.selectList.push(value.label);
          data.selectListNew.push(value.value);
        }
        data.selectList = data.selectList.filter((value, index, self) => {
          return self.indexOf(value) === index;
        });
        data.selectListNew = data.selectListNew.filter((value, index, self) => {
          return self.indexOf(value) === index;
        });
        data.labelIdlist = data.selectListNew;
        data.labelIdlist = data.labelIdlist.join(",");
        data.currentPage = 1

        if (data.switchOnOff) {
          if (data.name != '') {
            aiFilter()
          } else {
            data.currentPage = 1;
            getList();
          }
        } else {
          data.currentPage = 1;
          getList();
        }
      }
    };
    const providerBtn = (val) => {
      if (data.deliverList.includes(val.value)) {
        data.deliverList = data.deliverList.filter((value, index) => {
          return value !== val.value;
        });
      } else {
        data.deliverList.push(val.value);
      }
      if (data.providerSelect.includes(val.value)) {
        data.providerSelect = data.providerSelect.filter((item, index) => {
          return item !== val.value;
        });
      } else {
        data.providerSelect.push(val.value);
        data.providerSelect = data.providerSelect.filter(
          (value, index, self) => {
            return self.indexOf(value) === index;
          }
        );
      }
      data.currentPage = 1;
      if (data.switchOnOff) {
        if (data.name != '') {
          aiFilter()
        } else {
          getList()
        }
      } else {
        getList()
      }
      // getList();
    };

    //能力提供方筛选
    const providerClick = (val, index) => {
      // if (data.providerName == val.label) {
      //   data.providerName = ""
      // } else {
      //   data.providerName = val.label;
      // }
      data.currentPage = 1
      if (data.providerName == val.label) {
        data.providerName = "";
        if (data.switchOnOff) {
          if (data.name != '') {
            aiFilter()
          } else {
            getList()
          }
        } else {
          getList()
        }
      } else {
        data.providerName = val.label;
        if (data.switchOnOff) {
          if (data.name != '') {
            aiFilter()
          } else {
            getList();
          }
        } else {
          getList();
        }
      }
      // getList();
    }

    const deleteSelect = (val, index, type) => {
      if (type == "provider") {
        data.providerName = "";
        data.currentPage = 1
        if (data.switchOnOff) {
          if (data.name != '') {
            aiFilter()
          } else {
            getList();
          }
        } else {
          getList()
        }
        return false;
      }
      if (type == "pro") {
        data.providerSelect = [];
        data.activeKey = "";
      }
      if (type == "type") {
        data.abilitySelect = [];
        data.activeKeyAbility = "";
      }
      if (type == "label") {
        data.activeLabel = "";
        data.activeLabelList = [];
      }
      if (type == "pay") {
        data.providerSelect.splice(index, 1);
        data.deliverList.splice(index, 1);
      }
      data.selectList.splice(index, 1);
      data.selectListNew.splice(index, 1);
      data.labelIdlist = data.selectListNew;
      data.labelIdlist = data.labelIdlist.join(",");
      data.currentPage = 1
      if (data.switchOnOff) {
        if (data.name != '') {
          aiFilter()
        } else {
          getList()
        }
      } else {
        getList();
      }
    };
    const showMore = (type, index) => {
      if (type == "provider") {
        data.showScense = type;
        data.morePro = false;
      } else if (type == "abilityType") {
        data.showScense = type;
        data.abilityPro = false;
      } else {
        data.showIndex = index;
        data.showScense = type;
      }
    };
    const showless = (type, index) => {
      if (type == "provider_less") {
        data.showScense = type;
        data.morePro = true;
      } else if (type == "abilityType_less") {
        data.showScense = type;
        data.abilityPro = true;
      } else {
        data.showIndex = "";
        data.showScense = type;
      }
    };
    const typeBtn = (item) => {
      data.activeKeyAbility = item.value;
      let str = "产品类型：" + item.label;
      if (data.abilitySelect.includes(str)) {
        data.abilitySelect = [];
        data.activeKeyAbility = "";
      } else {
        data.abilitySelect = data.abilitySelect.filter((value, index, self) => {
          return self.indexOf(value) === index;
        });
        data.abilitySelect = [];
        data.abilitySelect.push(str);
      }
      data.currentPage = 1;
      getList();
    };
    //getTarde();

    //AI结果分页
    const getAIPageList = () => {
      let firstPageNum = (data.currentPage - 1) * data.pageItemSize;
      let lastPageNum = data.currentPage * data.pageItemSize;
      data.tableList = data.tableAIAllList.slice(firstPageNum, lastPageNum);
    }

    const getAIList = (combineName) => {
      if (data.name.trim() == "") {
        data.showPagination = true;
        data.currentPage = 1
        getList();
        return false
      }

      data.providerName = ''
      data.activeKey = "";
      data.showId = '';
      data.showScense = "";
      data.showIndex = '';
      data.showLast = false;
      data.deliverList = []
      data.labelIdlist = [];
      data.providerSelect = [];
      data.selectList = [];
      data.selectListNew = [];

      data.loadingShow = true;
      data.AIsearch = true;
      let params = {
        question: combineName ? combineName : data.name,
        type: 3,
        limitedType: combineName ? 'E' : data.contentType,
      }
      if(data.searchContent.label != ''){
        params.question = data.searchContent.label + '的' + data.name
        params.limitedType = 'E'
      }
      AISearch(params).then((res) => {
        data.loadingShow = false;
        data.AIsearch = false;
        if (res.code == 200) {
          data.tableAIAllList = [];
          data.tableList = [];
          data.tableAIAllList = res.data;
          data.showPagination = false;
          data.totalItemCount = data.tableAIAllList.length;
          getAIPageList();
          // data.tableList = data.tableAIAllList.slice(0, 10);
          // }
        }
      });
    };
    const refreshList = () => {
      if (!data.switchOnOff) {
        getList();
      } else {
        getAIList();
      }
    };
    eventBus.on("moduleAllRefresh", refreshList);
    const toggleShowLabel = () => {
      data.showLabel = !data.showLabel;
    };

    // 开始录音的函数
    const transformVoice = () => {
      // if (data.isRecording) {
      //   recorder.stop(); // 停止录音
      //   data.isRecording = false;
      //   console.log('录音停止');
      //   // 当录音停止时，处理录音数据
      //   recorder.onstop = () => {
      //     // 将录音数据块合并成一个 Blob (wav 格式)
      //     const audioBlob = new Blob(audioChunks, { type: 'audio/wav' });
      //     // 创建 FormData 对象并添加录音文件
      //     const formData = new FormData();
      //     formData.append('file', audioBlob, 'recording.wav'); // 字段名为 "file"
      //     // 调用 AIvoice 函数并传入 FormData
      // AIvoice(formData).then(res => {
      //   console.log(res)
      //   audioChunks = []
      //   data.name = res.msg
      //   seekContent()
      // });
      //   };
      // } else {
      //   navigator.mediaDevices.getUserMedia({ audio: true })
      //     .then((stream) => {
      //       // 初始化 MediaRecorder
      //       recorder = new MediaRecorder(stream);
      //       // 清空之前的音频数据块
      //       // 当有录音数据时，将其添加到音频块数组
      //       recorder.ondataavailable = (event) => {
      //         audioChunks.push(event.data);
      //       };
      //       // 开始录音
      //       recorder.start();
      //       data.isRecording = true;
      //       console.log('录音开始');
      //     })
      //     .catch((error) => {
      //       console.error('无法获取音频流', error);
      //     });
      // }
    };
    // 语音输入
    const handleAudio = (audioBlob) => {
      const formData = new FormData();
      formData.append("file", audioBlob, "recording.wav"); // 上传文件
      console.log("ssssssss", formData);
      // 调用 AIvoice 函数并传递音频数据
      data.isTranslating = true;
      data.canBtnUse = true;
      AIvoice(formData).then((res) => {
        data.isTranslating = false;
        data.canBtnUse = false;
        console.log(res);
        if (res.code == 200) {
          data.name = res.msg;
          // seekContent();
        }
      });
    };
    const abilityBtn = (e) => {
      if (data.activeLabel == e.name) {
        data.activeLabel = "";
        data.activeLabelList = [];
      } else {
        data.activeLabelList = [];
        let str = "能力种类：" + e.name;
        data.activeLabel = e.name;
        data.activeLabelList.push(str);
      }
      data.currentPage = 1;
      getList();
    };

    //生态合作方搜索
    const searchChange = debounce(function () {
      getSearchRes(data.searchContent.label)
    }, 1000)
    function debounce(fn, delay) {
      let time = null
      return function () {
        if (time) {
          clearTimeout(time)
        }
        time = setTimeout(() => {
          fn.call(this)
        }, 600);
      }
    }
    const getSearchRes = (val) => {
      let submitData = {
        name: val
      }
      if (val == '') {
        searchList.value = []
        data.searchContent.id = ''
        data.currentPage = 1
        if (data.switchOnOff) {
          if (data.name != '') {
            getAIList()
          } else {
            data.currentPage = 1;
            getList();
          }
        } else {
          data.currentPage = 1;
          getList();
        }
      } else {
        getSearchList(submitData).then(res => {
          if (res.code == 200) {
            searchList.value = res.data.slice(0, 50)
            searchShow.value = true
          }
        })
      }
    }
    const setName = (value) => {
      data.searchContent.label = value.name;
      data.searchContent.id = value.id;
      if(data.name != '' && data.switchOnOff){
        let combineName = data.searchContent.label + '的' + data.name
        getAIList(combineName);
      }
      if(data.name == '' || (!data.switchOnOff && data.name != '')){
        getList();
      }
    };

    const clickBlur = () => {
      searchShow.value = false
    }
    const clickOn = () => {
      searchShow.value = true
    }
    const chooseContentType = (type) => {
      data.searchContent.label = ''
      data.searchContent.id = ''
      data.contentType = type;
    };
    // 鼠标悬浮事件处理
    const handleMouseEnter = () => {
      if (data.switchOnOff) {
        if (data.contentType == 'K') {
          data.searchText = '搜能力';
        } else {
          data.searchText = '搜生态';
        }
        data.showEcoSearch = true;
        data.showDownIcon = false;
        data.isHover = true;
      }
    };

    const handleMouseLeave = () => {
      data.searchText = '搜索';
      data.showEcoSearch = false;
      data.showDownIcon = true;
      data.isHover = false;
    };
    return {
      clickOn,
      clickBlur,
      ...toRefs(data),
      searchList,
      searchShow,
      setName,
      debounce,
      getSearchRes,
      searchChange,
      toggleShowLabel,
      abilityBtn,
      vocation,
      region,
      labelSelect,
      typeBtn,
      providerBtn,
      showMore,
      showless,
      deleteSelect,
      regionChange,
      tabChange,
      add,
      contentColor,
      labelChange,
      getLabel,
      sizeChange,
      contentLeave,
      proDetail,
      router,
      pageChange,
      zhCN,
      seekContent,
      baseURL,
      getAIList,
      refreshList,
      transformVoice,
      handleAudio,
      providerClick,
      chooseContentType,
      handleMouseEnter,
      handleMouseLeave,
      changeSearch,
    };
  },
});
</script>

<style lang="scss" scoped src="./tableList.scss"></style>

<style lang="scss">
.mypage {
  .ant-pagination {
    color: rgba(0, 0, 0, 0.65);
  }

  .ant-select-selector {
    color: rgba(0, 0, 0, 0.65);
  }

  .ant-pagination-item-active {
    background: #007eff;
  }

  .ant-pagination-item-active a {
    color: #ffffff;
  }

  .ant-pagination-item-active:focus a,
  .ant-pagination-item-active:hover a {
    color: #ffffff;
  }

  .ant-pagination-item-active a:focus,
  .ant-pagination-item-active a:hover {
    color: #ffffff;
  }

  .ant-pagination-item:focus,
  .ant-pagination-item:hover {
    background: #007eff;
  }

  .ant-pagination-item:focus a,
  .ant-pagination-item:hover a {
    color: #ffffff;
  }

  .ant-pagination-item-disabled:hover,
  .ant-pagination-item-disabled:focus {
    background-color: #ffffff;
    border-color: #d9d9d9;

    a {
      color: rgba(0, 0, 0, 0.25);
    }
  }

  .ant-pagination-options-size-changer.ant-select {
    margin-right: 0;
  }

  .ant-pagination-options-quick-jumper {
    margin-left: 8px;
  }
}

/*.shopping {
  position: absolute;
  right: 4px;
  bottom: 12px;
}*/

.ant-select:not(.ant-select-customize-input) .ant-select-selector {
  border: none !important;
}

.custom-placeholder .ant-select-selection-placeholder {
  color: #000;
}

.AISearchLogo {
  width: 190px;
  height: 80px;

  img {
    width: 100%;
    height: 100%;
  }
}

.chooseSearchType {
  background: #ffffff;
  height: 56px;

  .border_blue {
    border: 2px solid #848EFD;
  }

  .border_green {
    border: 2px solid #72F7FF;
  }

  .chooseContent {
    width: 164px;
    height: 32px;
    border-radius: 32px;
    margin-right: 17px;
    padding: 4px;
    font-size: 14px;

    .solutionBox {
      width: 76px;
      height: 24px;
      border-radius: 24px;
    }

    .shengtaiBox {
      width: 76px;
      height: 24px;
      border-radius: 24px;
    }

    .choosedWord {
      color: #ffffff;
    }

    .nochoosedWord {
      color: #A2ABB5;
    }

    .solutionBg {
      background: linear-gradient(108deg, #4446FF 0%, #4173FF 50%, #3EB8FF 100%);
    }

    .shengtaiBg {
      background: linear-gradient(108deg, #02BFE1 0%, #0AD1D7 50%, #6CEBF5 100%);
    }
  }
}
</style>
