<template>
  <div class="modelContent">
    <div class="title">
      <img
        src="@/assets/images/community/arrowRight.png"
        alt=""
        width="14px"
        height="8px"
        style="margin-right: 10px"
      />
      <img
        src="@/assets/images/community/word.png"
        alt=""
        width="72px"
        height="18px"
      />
    </div>
    <a-row class="margin_t_32">
      <a-col :span="8">
        <span class="referName">提交人：</span>
        <span class="textName">{{ dataDetail.createName }}</span>
      </a-col>
      <a-col :span="8">
        <span class="referName">联系方式：</span>
        <span class="textName">{{ dataDetail.phone }}</span>
      </a-col>
      <a-col :span="8">
        <span class="referName">所属单位：</span>
        <span class="textName">{{ dataDetail.orgName }}</span>
      </a-col>
    </a-row>

    <div class="margin_t_20">
      <div class="card_table">
        <div class="table_left">
          <div class="margin_r_16 margin_t_12">主题</div>
        </div>
        <div class="table_right">
          <div class="margin_t_12">{{ dataDetail.title }}</div>
        </div>
      </div>
      <div class="card_table">
        <div class="table_left">
          <div class="margin_r_16 margin_t_12">提交人</div>
        </div>
        <div class="table_right">
          <div class="margin_t_12">{{ dataDetail.createName }}</div>
        </div>
        <div class="table_left">
          <div class="margin_r_16 margin_t_12">提交类型</div>
        </div>
        <div class="table_right">
          <div class="margin_t_12">{{ dealType(dataDetail.createName) }}</div>
        </div>
        <div class="table_left">
          <div class="margin_r_16 margin_t_12">发布日期</div>
        </div>
        <div class="table_right">
          <div class="margin_t_12">{{ dataDetail.createTime }}</div>
        </div>
      </div>
      <div class="card_table">
        <div class="table_left">
          <div class="margin_r_16 margin_t_12">内容</div>
        </div>
        <div class="table_right">
          <div class="margin_t_12 margin_b_12" style="white-space: pre-wrap">
            {{ dataDetail.content }}
          </div>
        </div>
      </div>
      <div class="card_table">
        <div class="table_left table_line">
          <div class="margin_r_16 margin_t_12">附件</div>
        </div>
        <div class="table_right table_line">
          <div
            class="margin_t_12 tableFile"
            v-if="dataDetail.file && dataDetail.file.fileName"
            @click="previewFile(dataDetail.file)"
          >
            {{ dataDetail.file.fileName }}
          </div>
          <div class="margin_t_12" v-else>暂无</div>
        </div>
      </div>
    </div>

    <a-form ref="allotRef" :model="formData" labelAlign="right" :rules="rules">
      <a-row class="margin_t_32 flex just-sb">
        <a-form-item label="主办单位" name="sponsorOrgId">
          <a-select
            v-model:value="formData.sponsorOrgId"
            placeholder="请选择主办单位"
            allowClear
            @change="handleCompanyChange('main')"
            @blur="allotRef.clearValidate(['sponsorOrgId'])"
          >
            <a-select-option
              v-for="item in orgList"
              :key="item.id"
              :value="item.id"
            >
              {{ item.name }}
            </a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item label="主办人">
          <a-input v-model:value="formData.sponsorName" disabled />
        </a-form-item>
      </a-row>

      <template v-if="dataDetail.hostReject != '1'">
        <div v-for="(item, index) in subCompanies" :key="index">
          <a-row class="flex just-sb">
            <a-form-item :label="`协办单位${index + 1}`">
              <a-select
                :placeholder="`请选择协办单位${index + 1}`"
                v-model:value="item.orgId"
                allowClear
                @change="handleCompanyChange('sub', index)"
              >
                <a-select-option
                  v-for="org in getAvailableOrgs(index)"
                  :key="org.id"
                  :value="org.id"
                >
                  {{ org.name }}
                </a-select-option>
              </a-select>
            </a-form-item>

            <a-form-item :label="`协办人${index + 1}`">
              <a-input v-model:value="item.userName" disabled />
            </a-form-item>
          </a-row>
        </div>
      </template>
    </a-form>
    <div class="flex just-center">
      <a-button
        class="margin_r_10"
        @click="reject"
        style="
          background: linear-gradient(90deg, #ff845f 0%, #ff3730 100%);
          border-radius: 4px;
          font-weight: 500;
          border: none;
          color: #ffffff;
          margin-right: 16px;
        "
        >驳回
      </a-button>
      <a-button
        type="primary"
        style="
          background: linear-gradient(270deg, #0142fd 0%, #2475f9 100%);
          border-radius: 4px;
          font-weight: 500;
          border: none;
          color: #ffffff;
        "
        :loading="addLoading"
        @click="toNext"
        >提交
      </a-button>
    </div>
  </div>
</template>
<script>
import { defineComponent, reactive, toRefs, ref, computed, watch } from "vue";
import { getOrgList, setAllocation } from "@/api/community/index.js";
import { useRouter } from "vue-router";
import { message } from "ant-design-vue";
export default defineComponent({
  emits: ["reject-view", "preview-file"],
  props: {
    dataDetail: {
      type: Object,
      default: () => ({
        createName: "",
        phone: "",
        orgName: "",
        title: "",
        content: "",
        file: null,
        id: null,
        hostReject: null,
      }),
    },
  },
  setup(props, { emit }) {
    const allotRef = ref(null);
    const Router = useRouter();
    const data = reactive({
      dataDetail: computed(() => props.dataDetail || ""),
      auditLoading: false,
      orgList: [],
      formData: {
        sponsorOrgId: undefined,
        sponsorUserId: undefined,
        sponsorName: "",
      },
      addLoading: false,
      subCompanies: [
        { orgId: undefined, userId: undefined, userName: "" },
        { orgId: undefined, userId: undefined, userName: "" },
      ],
    });
    const reject = () => {
      emit("reject-view");
    };
    watch(
      () => allotRef.value,
      (newVal) => {
        console.log("Form ref changed:", newVal);
      }
    );

    watch(
      () => formData.sponsorOrgId,
      (newVal) => {
        console.log("sponsorOrgId changed:", newVal);
      }
    );
    const rules = reactive({
      sponsorOrgId: [
        {
          required: true,
          message: "请选择主办部门",
          trigger: "blur",
          validator: (rule, value) => {
            return value ? Promise.resolve() : Promise.reject("请选择主办部门");
          },
        },
      ],
    });
    const dealType = (v) => {
      switch (v) {
        case 1:
          return "需求建议";
        case 2:
          return "官方公告";
        case 3:
          return "技术交流";
        case 4:
          return "经验分享";
        case 5:
          return "问答互勉";
        default:
          return v;
      }
    };
    const previewFile = async (e) => {
      try {
        const { fileUrl: href, fileName: downName } = e;
        const token = localStorage.getItem("token") || "";
        const fileExtension = downName.split(".").pop().toLowerCase();
        const isImage = ["jpg", "jpeg", "png"].includes(fileExtension);
        const isVideo = ["mp4", "mov", "avi", "wmv", "mkv", "flv"].includes(
          fileExtension
        );
        if (isVideo) {
          let type = 1;
          emit("preview-file", e, type);
        } else if (isImage) {
          let type = 2;
          emit("preview-file", e, type);
        } else {
          let downloadUrl = new URL(href, window.location.origin).href;
          if (href.includes(window.location.origin)) {
            downloadUrl = href.replace(window.location.origin, "/portal");
          }
          const urlObj = new URL(downloadUrl);
          urlObj.searchParams.set("token", token);
          downloadUrl = urlObj.toString();
          window.open(downloadUrl, "_blank");
        }
      } catch (error) {
        console.error("文件处理失败:", error);
      }
      return false;
    };

    const getOrg = async () => {
      try {
        const res = await getOrgList();
        data.orgList = res.data.map((element) => ({
          id: element.orgId,
          name: element.orgName,
          userId: element.userId,
          userName: element.realName,
        }));
      } catch (error) {
        console.error("获取组织列表失败:", error);
        message.error("获取组织列表失败");
      }
    };
    const getAvailableOrgs = (subIndex) => {
      const selectedOrgIds = new Set([data.formData.sponsorOrgId]);
      data.subCompanies.forEach((item, idx) => {
        if (idx !== subIndex && item.orgId) {
          selectedOrgIds.add(item.orgId);
        }
      });
      return data.orgList.filter((org) => !selectedOrgIds.has(org.id));
    };

    const handleCompanyChange = async (type, index) => {
      // 清除校验状态
      allotRef.value.clearValidate(["sponsorOrgId"]);

      if (type === "main") {
        if (!data.formData.sponsorOrgId) {
          // 清空主办单位时，同时清空主办人信息
          data.formData.sponsorUserId = undefined;
          data.formData.sponsorName = "";
        } else {
          const selectedOrg = data.orgList.find(
            (org) => org.id === data.formData.sponsorOrgId
          );
          if (selectedOrg) {
            data.formData.sponsorUserId = selectedOrg.userId;
            data.formData.sponsorName = selectedOrg.userName;
          }
        }

        // 重置协办单位
        data.subCompanies = data.subCompanies.map(() => ({
          orgId: undefined,
          userId: undefined,
          userName: "",
        }));
      } else {
        if (!data.subCompanies[index].orgId) {
          // 清空协办单位时，同时清空协办人信息
          data.subCompanies[index].userId = undefined;
          data.subCompanies[index].userName = "";
        } else {
          const selectedOrg = data.orgList.find(
            (org) => org.id === data.subCompanies[index].orgId
          );
          if (selectedOrg) {
            data.subCompanies[index].userId = selectedOrg.userId;
            data.subCompanies[index].userName = selectedOrg.userName;
          }
        }
      }
    };
    const toNext = async () => {
      try {
        // 先进行表单校验
        await allotRef.value.validateFields(["sponsorOrgId"]);

        data.addLoading = true;
        const subUserIds = data.subCompanies
          .filter((item) => item.userId)
          .map((item) => item.userId);

        const params = {
          id: data.dataDetail?.id,
          mainUserId: data.formData.sponsorUserId,
          auditResult: 1,
          subUserIds: subUserIds,
        };

        const res = await setAllocation(params);
        if (res.code === 200) {
          message.success("分配成功");
          emit("modal-close");
          Router.push({
            name: "communityHome",
          });
        }
      } catch (error) {
        if (error.errorFields) {
          // 校验失败
          message.error("请完成必填项");
        } else {
          console.error("分配失败:", error);
          message.error("分配失败");
        }
      } finally {
        data.addLoading = false;
      }
    };

    getOrg();

    return {
      ...toRefs(data),
      allotRef,
      reject,
      toNext,
      dealType,
      previewFile,
      getOrg,
      handleCompanyChange,
      getAvailableOrgs,
      Router,
      rules,
    };
  },
});
</script>
<style lang="scss" scoped>
::v-deep(.ant-form-item-label) {
  width: 80px;
  font-weight: 500;
  color: #00060e;
  text-align: right;
}
::v-deep(.ant-select) {
  width: 433px;
}
::v-deep(.ant-input) {
  width: 433px;
}
.modelContent {
  margin: 0 16px;
}
.title {
  margin-top: -8px;
}
.referName {
  font-weight: 500;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.85);
}
.textName {
  font-weight: 400;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.85);
}
.card_table {
  display: flex;

  .table_left {
    width: 160px;
    min-height: 46px;
    background: #f5f7fc;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.65);
    text-align: right;
    border: 1px solid #e8eaed;
    border-right: none;
    border-bottom: none;
  }

  .table_right {
    flex: 1;
    min-height: 46px;
    color: rgba(0, 0, 0, 0.85);
    border: 1px solid #e8eaed;
    padding: 0 16px;
    border-left: none;
    border-bottom: none;
  }

  .table_line {
    border-bottom: 1px solid #e8eaed;
  }

  .tableFile {
    color: #0c70eb;
    border-bottom: 1px solid #0c70eb;
    width: fit-content;
    cursor: pointer;
  }
}
</style>