
<template>
  <div class="searchInfo">
    <div class="vocationPull">
      <a-config-provider
        :locale="zhCN"
        :getPopupContainer="(triggerNode) => triggerNode.parentNode"
      >
        <a-input
          v-model:value="searchParams.name"
          class="inputClass"
          allow-clear
          @keyup.enter="seekContent"
          placeholder="请输入工具名称、描述等关键词进行搜索"
        />
        <div style="margin: 10px 16px 10px 12px; width: 220px">
          <a-select
            :value="searchParams.type"
            style="width: 100%"
            :bordered="false"
            @change="vocationChange"
            :options="vocationList"
          >
            <template #suffixIcon>
              <img
                src="@/assets/images/home/<USER>"
                style="width: 16px; height: 16px; margin-top: -5px"
              />
            </template>
          </a-select>
        </div>

        <div class="seekInfo" @click="seekContent">
          <img src="@/assets/images/home/<USER>" />
          <div>搜索</div>
        </div>

        <div class="seekInfo reset" @click="reset">
          <img src="@/assets/images/make/reset.svg" />
          <div>重置</div>
        </div>
      </a-config-provider>
    </div>
  </div>

  <div class="tabContent">
    <div class="tabModel">
      <div
        @click="allList()"
        :class="[
          'tab_text',
          {
            topTab: true,
          },
        ]"
      >
        <p style="margin-left: 40px">全部（{{ totalItemCount }}）</p>
      </div>
      <div class="tree">
        <a-tree
          class="draggable-tree"
          v-model:expandedKeys="expandedKeys"
          draggable
          show-icon
          :tree-data="tabList"
          default-expand-all
          @select="changeNode"
        >
          <template v-slot:title="treeItem">
            <div :class="[{ selectedTree: treeItem.selected }]">
              <span
                :class="{
                  isChildren: treeItem.isChildren,
                }"
                >{{ treeItem.title }}
              </span>
            </div>
          </template>
        </a-tree>
      </div>
    </div>
    <div v-if="tableList && tableList.length > 0" style="width: 100%">
      <div class="cardContent">
        <div class="card_total flex-1">
          <template v-for="(item, index) in tableList" :key="index">
            <div
              :class="[
                'card_content',
                {
                  cardActive: cardActive == index,
                  rightActive: index % 2 != 0,
                  cardObvious: index < 2 && tableList.length < 3,
                  bottomLine:
                    (index == tableList.length - 1 ||
                      index == tableList.length - 2) &&
                    index > 1,
                },
              ]"
              @mouseenter="contentColor(index)"
              @mouseleave="contentLeave"
              @click="proDetail(item)"
            >
              <div style="display: flex; margin: 24px">
                <div>
                  <a-image
                    :width="168"
                    :height="105"
                    :preview="false"
                    v-if="item.coverUrl"
                    :src="`${item.coverUrl}`"
                  />
                  <img
                    src="@/assets/images/home/<USER>"
                    style="width: 168px; height: 105px"
                    v-else
                  />
                </div>
                <div class="card_center">
                  <div class="card_text">
                    <div class="card_tag">
                      <div class="card_title">{{ item.name }}</div>
                    </div>
                  </div>
                  <div class="card_des">
                    {{ item.description }}
                  </div>
                  <div
                    style="
                      display: flex;
                      align-items: center;
                      justify-content: space-between;
                    "
                  >
                    <img
                      v-if="item.type == '研发工具'"
                      src="@/assets/images/make/tool.svg"
                      style="width: 70px; height: 22px"
                    />
                    <img
                      v-else
                      src="@/assets/images/make/develop.svg"
                      style="width: 70px; height: 22px"
                    />
                    <div
                      style="
                        display: flex;
                        align-items: center;
                        justify-content: space-between;
                      "
                    >
                      <div
                        style="
                          display: flex;
                          align-items: center;
                          margin-right: 18px;
                        "
                      >
                        <img
                          src="@/assets/images/home/<USER>"
                          style="width: 16px; height: 16px"
                        />
                        <span
                          style="font-size: 12px; color: rgba(0, 0, 0, 0.45)"
                          >{{ item.viewCount }}</span
                        >
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </template>
        </div>
      </div>
      <div class="layPage">
        <a-pagination
          v-model:pageSize="searchParams.pageSize"
          v-model:current="searchParams.pageNo"
          :pageSizeOptions="pageSizeOptions"
          show-quick-jumper
          show-size-changer
          :total="totalItemCount"
          @change="pageChange"
          @showSizeChange="sizeChange"
          class="mypage"
        />
      </div>
    </div>
    <div v-if="tableList && tableList.length == 0" class="emptyPhoto">
      <img src="@/assets/images/home/<USER>" />
    </div>
    <div class="loading" v-show="loadingShow">
      <a-spin />
    </div>
  </div>
  <!-- 详情弹框 -->
  <a-modal
    :visible="previewVisible"
    :footer="null"
    :id="id"
    @cancel="closeModal"
    :width="800"
    :destroyOnClose="true"
    :maskClosable="false"
  >
    <template v-slot:title="title">
      <img src="@/assets/images/make/titleIcon.svg" />
      {{ modalTitle }}
    </template>
    <detail-modal :id="id"></detail-modal>
  </a-modal>
</template>

<script>
import { defineComponent, reactive, toRefs, ref } from "vue";
import { useRouter } from "vue-router";
import zhCN from "ant-design-vue/es/locale/zh_CN";
import { getMakeUrl } from "@/utils/getUrl";
import detailModal from "./detailModal.vue";
import { getMakeList } from "@/api/makeUp/make.js";
export default defineComponent({
  components: { detailModal },
  setup() {
    const baseURL = getMakeUrl();
    const vocation = ref("");
    const data = reactive({
      keyword: "",
      moment: "",
      loadingShow: true,
      activeKey: 0,
      modalTitle: "",
      id: "",
      previewVisible: false,
      cardActive: "-1",
      pageSizeOptions: ["10", "20", "30", "50"],
      totalItemCount: 0,
      expandedKeys: [1, 2],
      checkedKeys: [],
      selectedKeys: ["0"],
      tabList: [
        {
          title: "研发协同",
          key: 1,
          children: [{ title: "磐舟DevSecOps平台", key: 11, isChildren: true }],
        },
        {
          title: "研发工具",
          key: 2,
          children: [
            { title: "页面编排", key: 21, isChildren: true },
            { title: "大屏编排", key: 22, isChildren: true },
          ],
        },
      ],
      vocationList: [
        {
          label: "全部分类",
          value: "",
        },
        {
          label: "研发协同",
          value: "研发协同",
        },
        {
          label: "研发工具",
          value: "研发工具",
        },
      ],
      tableList: [],
      totalNum: 0,
      searchParams: {
        pageNo: 1,
        pageSize: 10,
        name: "",
        type: "",
      },
    });
    const getList = (nodeParams) => {
      data.loadingShow = true;
      getMakeList(nodeParams || data.searchParams)
        .then((res) => {
          if (res.code === 200) {
            data.loadingShow = false;
            data.tableList = res.data.rows;
            data.totalItemCount = res.data.totalRows;
          }
        })
        .catch((error) => {
          data.loadingShow = false;
        });
    };
    getList();

    const seekContent = () => {
      getList();
    };

    const allList = () => {
      data.searchParams.name = "";
      data.searchParams.type = "";
      getList();
    };
    const vocationChange = (value) => {
      data.searchParams.type = value;
      data.pageNo = 1;
      getList();
    };

    const changeNode = (selectedKeys, { selectedNodes }) => {
      let selectObject = selectedNodes[0].props.dataRef;
      // 子集传name搜索
      let params = { pageNo: 1, pageSize: 10 };
      if (selectObject.isChildren) {
        params["name"] = selectObject.title;
        params["type"] = "";
      } else {
        params["type"] = selectObject.title;
        params["name"] = "";
      }
      getList(params);
    };
    const reset = () => {
      data.searchParams = {
        pageNo: 1,
        pageSize: 10,
        name: "",
        type: "",
      };
      getList();
    };

    const contentColor = (index) => {
      data.cardActive = index;
    };
    const router = useRouter();
    const proDetail = (item) => {
      data.id = item.id;
      data.modalTitle = item.name;
      data.previewVisible = true;
    };

    const closeModal = () => {
      data.previewVisible = false;
    };

    const contentLeave = () => {
      data.cardActive = "-1";
    };
    const pageChange = (page, pageSize) => {
      data.searchParams.pageNo = page;
      getList();
    };
    const sizeChange = (current, size) => {
      data.searchParams.pageSize = size;
    };

    return {
      ...toRefs(data),
      vocation,
      reset,
      allList,
      closeModal,
      vocationChange,
      changeNode,
      contentColor,
      sizeChange,
      contentLeave,
      proDetail,
      router,
      pageChange,
      zhCN,
      seekContent,
      baseURL,
    };
  },
});
</script>

<style lang="scss" scoped src="./tableList.scss"></style>

<style  lang="scss">
.mypage {
  .ant-pagination {
    color: rgba(0, 0, 0, 0.65);
  }

  .ant-select-selector {
    color: rgba(0, 0, 0, 0.65);
  }

  .ant-pagination-item-active {
    background: #007eff;
  }

  .ant-pagination-item-active a {
    color: #ffffff;
  }

  .ant-pagination-item-active:focus a,
  .ant-pagination-item-active:hover a {
    color: #ffffff;
  }

  .ant-pagination-item-active a:focus,
  .ant-pagination-item-active a:hover {
    color: #ffffff;
  }

  .ant-pagination-item:focus,
  .ant-pagination-item:hover {
    background: #007eff;
  }

  .ant-pagination-item:focus a,
  .ant-pagination-item:hover a {
    color: #ffffff;
  }

  .ant-pagination-item-disabled:hover,
  .ant-pagination-item-disabled:focus {
    background-color: #ffffff;
    border-color: #d9d9d9;
    a {
      color: rgba(0, 0, 0, 0.25);
    }
  }

  .ant-pagination-options-size-changer.ant-select {
    margin-right: 0;
  }

  .ant-pagination-options-quick-jumper {
    margin-left: 8px;
  }
}
</style>