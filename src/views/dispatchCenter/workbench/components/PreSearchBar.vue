<template>
  <div class="search_form flex just-sb">
    <a-form class="basicForm" labelAlign="left" :model="formData">
      <div class="flex search_field just-start flex-wrap">
        <a-form-item class="form_item">
          <a-input
            @keyup.enter="submit"
            v-model:value="formData.title"
            placeholder="请输入工单标题"
            class="inner-text"
            allowClear
          />
        </a-form-item>

        <a-form-item class="form_item">
          <a-input
            @keyup.enter="submit"
            v-model:value="formData.projectName"
            placeholder="请输入项目名称"
            allowClear
          />
        </a-form-item>

        <a-form-item class="form_item">
          <a-select
            placeholder="请选择工单类型"
            v-model:value="formData.type"
            allowClear
          >
            <template v-for="(opt, index) in typeOptions" :key="index">
              <a-select-option :value="opt.value">
                {{ opt.label }}
              </a-select-option>
            </template>
          </a-select>
        </a-form-item>

        <a-form-item class="form_item">
          <a-input
            @keyup.enter="submit"
            v-model:value="formData.createName"
            placeholder="请输入提交人员姓名"
            class="inner-text"
            allowClear
          />
        </a-form-item>

        <a-form-item class="form_item">
          <el-config-provider :locale="locale">
            <el-date-picker
              v-model="createTimeRange"
              type="daterange"
              unlink-panels
              range-separator="~"
              start-placeholder="提交开始日期"
              end-placeholder="提交截止日期"
              :shortcuts="shortcuts"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              style="width: 100%"
              @change="onDateChange"
              placement="bottom-start"
              :suffix-icon="Calendar"
              :prefix-icon="null"
            />
          </el-config-provider>
        </a-form-item>

        <a-form-item class="button-item">
          <a-button class="reset_btn default_btn" @click="reset">
            重置
          </a-button>
          <a-button type="primary" @click="submit"> 搜索 </a-button>
        </a-form-item>
      </div>
    </a-form>
  </div>
</template>

<script>
import { reactive, toRefs, defineComponent } from "vue";
import { Calendar } from "@element-plus/icons-vue";
import zhCn from "element-plus/es/locale/lang/zh-cn";
export default defineComponent({
  name: "SearchForm",
  emits: ["form-search"],
  components: {
    Calendar,
  },
  props: {},
  setup(props, { emit }) {
    const data = reactive({
      createTimeRange: [],
      formData: {
        title: "",
        projectName: "",
        createName: "",
        type: null,
        createTimeStart: null,
        createTimeEnd: null,
      },

      typeOptions: [
        { label: "售前工单", value: 8 },
        { label: "售中工单", value: 9 },
      ],
      // businessTypeOptions: [
      //   { label: "调度支撑", value: 11 },
      // ],
    });
    // 时间范围选中事件
    const onDateChange = (dates) => {
      if (dates && dates.length > 0) {
        data.formData.createTimeStart = dates[0] + " 00:00:00";
        data.formData.createTimeEnd =
          dates.length > 1 ? dates[1] + " 23:59:59" : dates[0] + " 23:59:59";
      } else {
        data.formData.createTimeStart = null;
        data.formData.createTimeEnd = null;
      }
    };

    // 重置
    const reset = () => {
      data.createTimeRange = [];
      data.formData = {
        title: "",
        projectName: "",
        type: null,
        createName: null,
        createTimeStart: null,
        createTimeEnd: null,
      };
      emit("form-search", data.formData);
    };

    const submit = () => {
      console.log("提交的表单数据:", data.formData);
      emit("form-search", data.formData);
    };
    const shortcuts = [
      {
        text: "最近一周",
        value: () => {
          const end = new Date();
          const start = new Date();
          start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
          return [start, end];
        },
      },
      {
        text: "最近一个月",
        value: () => {
          const end = new Date();
          const start = new Date();
          start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
          return [start, end];
        },
      },
      {
        text: "最近三个月",
        value: () => {
          const end = new Date();
          const start = new Date();
          start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
          return [start, end];
        },
      },
    ];
    const locale = ref(zhCn);
    return {
      ...toRefs(data),
      submit,
      reset,
      close,
      onDateChange,
      shortcuts,
      Calendar,
      locale,
    };
  },
});
</script>

<style lang="scss" scoped>
.search_form {
  position: relative;
  background: #ffffff;
  border-radius: 8px;
  padding: 24px 24px 16px 24px;
  margin-bottom: 24px;

  .search_field {
    flex-wrap: wrap;
    width: 100%;
  }

  .form_item {
    border: none;
  }

  .ant-form-item {
    margin: 0 12px;
    width: calc(25% - 24px);
    margin-bottom: 10px;
  }

  .ant-form-item-control-input {
    border-radius: 4px;
  }

  .button-item {
    margin-left: auto;
    margin-right: 12px;
    width: auto;
    display: flex;
    justify-content: flex-end;
    flex-shrink: 0;

    .ant-btn + .ant-btn {
      margin-left: 16px;
    }
  }

  ::v-deep(.ant-input) {
    background-color: #f5f6fa;
    border: none;
  }

  ::v-deep(.ant-select-selector) {
    background: #f5f6fa !important;
    border: none !important;
  }
  :deep(.el-date-editor) {
    background-color: #f5f6fa !important;
    border: none !important;
  }

  :deep(.el-input__wrapper) {
    background-color: #f5f6fa !important;
    border: none !important;
    box-shadow: 0 0 0 0px !important;
  }
}

.btn-group {
  display: flex;
  justify-content: flex-end;
  gap: 16px;
  // margin-top: 8px;
}

.reset_btn {
  margin-right: 16px;
}

.default_btn {
  background: rgba(12, 112, 235, 0.08);
  border-radius: 4px 4px 4px 4px;
  color: #0c70eb;
  border-radius: 4px;
  border: none;
}

.default_btn:hover {
  background: #d3e8ff !important;
  color: #0c70eb;
}

.default_btn:focus {
  background: rgba(12, 112, 235, 0.08) !important;
  color: #0c70eb;
}

.ant-btn-primary {
  background: #0c70eb;
  border-radius: 4px;
  color: #fff !important;
  border: none;
}

.ant-btn-primary:hover {
  background: #509fff !important;
}

.ant-btn-primary:focus {
  background: #0c70eb !important;
}

.ant-switch-checked {
  background-color: #0c70eb;
}

:deep(.ant-select-selector) {
  border: none !important;
  border-radius: 4px !important;
  background: #f5f6fa !important;
}

::v-deep(.ant-input-affix-wrapper) {
  border: none !important;
  border-radius: 4px !important;
  background: #f5f6fa !important;
}
</style>
