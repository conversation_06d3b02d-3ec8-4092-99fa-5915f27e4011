<template>
  <div class="pre-sale-container">
    <PreSearchBar ref="searchBarRef" @form-search="handleSearch" class="search-form" />

    <!-- 表格内容 -->
    <div class="table-area">
      <div class="tab-export-wrapper">
        <a-tabs :activeKey="activeStatus" @change="onTabChange" class="status-tabs">
          <a-tab-pane key="todo" tab="待办"></a-tab-pane>
          <a-tab-pane key="done" tab="已办"></a-tab-pane>
          <!-- <a-tab-pane key="waiting" tab="待阅"></a-tab-pane> -->
          <!-- <a-tab-pane key="created" tab="已建"></a-tab-pane> -->
          <!-- <a-tab-pane key="draft" tab="草稿箱"></a-tab-pane> -->
        </a-tabs>
        <div class="btn-group">
          <a-button class="default_btn" @click="openExportModal" v-if="!isEcologicalPartner">导出</a-button>
        </div>
      </div>
      <component :is="currentComponent" :search-params="searchParams" />
    </div>
    <!-- <DispatchTypeModal v-model="showDispatchModal" @select="handleDispatchSelect" /> -->
     <!-- 导出弹窗 -->
    <a-modal v-model:visible="showExportModal" title="报表导出" width="400px" style="top: 20%" :footer="null"
      @close="handleExportCancel">
      <a-form labelAlign="right" :colon="false">
        <a-form-item label="工单类型选择" required>
          <a-select v-model:value="selectedExportType" placeholder="请选择工单类型" :options="orderTypes" allowClear />
        </a-form-item>
      </a-form>
      <div class="flex just-center margin_t_32">
        <a-button class="margin_r_10 custom_btn cancel_btn" @click="handleExportCancel">取消</a-button>
        <a-button class="custom_btn active_btn" type="primary" :disabled="!selectedExportType"
          @click="handleExportConfirm">
          确定
        </a-button>
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch, nextTick, inject } from "vue";
import Backlog from "../tables/backlog.vue";
import Completed from "../tables/completed.vue";
import PreSearchBar from "./PreSearchBar.vue";
import DispatchTypeModal from "@/components/DispatchTypeModal/index.vue";
import { message } from "ant-design-vue";
import { debounce } from 'lodash-es';
import {
  getClassificationData,
  processDispatchTodoListExport,
  processDispatchFinishedListExport,
  processPreTotalListExport,
} from "@/api/dispatchCenter/backlog&completed.js";
const isAdmin = computed(() => {
  const userInfo = JSON.parse(localStorage.getItem("userInfo"));
  return (
    userInfo && userInfo.roleKeyList && userInfo.roleKeyList.includes("admin")
  );
});
const isEcologicalPartner = computed(() => {
  const userInfo = JSON.parse(localStorage.getItem("userInfo"));
  return userInfo?.roleKeyList?.includes("ecologicalPartner");
});
const STORAGE_KEY_PRE_ACTIVE_STATUS = "dispatch_center_pre_activeStatus";
const activeStatus = ref(
  sessionStorage.getItem(STORAGE_KEY_PRE_ACTIVE_STATUS) || "todo"
);
const onTabChange = (key) => {
  activeStatus.value = key;
};
watch(activeStatus, (newVal) => {
  sessionStorage.setItem(STORAGE_KEY_PRE_ACTIVE_STATUS, newVal);
});
const searchParams = ref({});
const searchBarRef = ref(null);
const processTypeInfo = ref(null);

// 导出弹窗显示状态
const showExportModal = ref(false);
const selectedExportType = ref(null);
// 看板数据刷新
const refreshDashboard = inject('refreshDashboard');
const debouncedRefresh = debounce(() => {
  refreshDashboard && refreshDashboard();
}, 500);
// 工单类型选项，根据实际调整
const orderTypes = [
  { label: "售前工单", value: 8 },
  { label: "售中工单", value: 9 },
  // { label: "售后工单", value: "" },
];
const currentComponent = computed(() => {
  switch (activeStatus.value) {
    case "todo":
      return Backlog;
    case "done":
      return Completed;
    // case "all":
    //   return PreAllList;
    default:
      return Backlog;
  }
});

const handleSearch = (formData) => {
  searchParams.value = { ...formData };
  debouncedRefresh();
};
const exportApiMap = {
  todo: processDispatchTodoListExport,
  done: processDispatchFinishedListExport,
  // all: isAdmin.value ? processPreTotalListExport : null,
};
const openExportModal = () => {
  showExportModal.value = true;
  if (
    searchParams.value.type &&
    orderTypes.some((item) => item.value === searchParams.value.type)
  ) {
    selectedExportType.value = searchParams.value.type;
  } else {
    selectedExportType.value = null;
  }
};
const handleExportConfirm = () => {
  if (!selectedExportType.value) {
    message.warning("请选择工单类型");
    return;
  }
  showExportModal.value = false;

  let exportApi = exportApiMap[activeStatus.value];
  if (!exportApi) {
    message.error("当前状态不支持导出");
    return;
  }
  let params = {
    category: processTypeInfo.value?.code,
    ...searchParams.value,
    type: selectedExportType.value,
  };
  exportApi(params)
    .then((res) => {
      const href = res.msg;
      const windowOrigin = window.location.origin;
      const token = localStorage.getItem("token");
      let newHref = href;
      if (href.includes(windowOrigin)) {
        newHref = "/portal" + href.split(windowOrigin)[1];
      }
      window.open(windowOrigin + newHref + "?token=" + token);
    })
    .catch(() => {
      message.error("导出失败");
    });
};

const handleExportCancel = () => {
  showExportModal.value = false;
};
watch(activeStatus, (newVal) => {
  searchParams.value = {};
  if (searchBarRef.value) {
    searchBarRef.value.reset();
  }
});
onMounted(() => {
  const params = {
    pageNum: 1,
    pageSize: 10,
  };
  getClassificationData(params)
    .then((res) => {
      if (res.data && res.data.rows) {
        processTypeInfo.value = res.data.rows.find(
          (item) => item.categoryName === "售前调度工单"
        );
      }
    })
    .catch(() => {
      console.error("获取分类数据异常");
    });
});
</script>

<style lang="scss" scoped>
.pre-sale-container {
  .table-area{
    background: #fff;
    border-radius: 8px;
  }
  .tab-export-wrapper {
    position: relative;

    padding: 16px 0px 0px 0px;

    .status-tabs {
      font-weight: bold;
      :deep(.ant-tabs-tab) {
        font-size: 16px;
      }
      &::after {
        width: 100% !important;
        left: 0 !important;
        right: 0 !important;
        bottom: 0 !important;
      }
      :deep(.ant-tabs-ink-bar) {
        height: 4px !important;
        // margin-left: 20px;
      }
      :deep(.ant-tabs-tab:first-of-type) {
        margin-left: 24px;
      }
    }
    .btn-group {
      position: absolute;
      right: 24px; // 与父容器的padding-right对齐
      top: 50%; // 垂直居中
      transform: translateY(-50%);
    }

    .default_btn {
      color: #2475F9;
      border-radius: 4px;
      font-weight: 400;
      font-size: 14px;
      line-height: 22px;
      text-align: center;
      font-style: normal;
      text-transform: none;
      border: 1px solid #2475F9;

      &:hover {
        background: #d3e8ff !important;
      }

      &:focus {
        background: rgba(12, 112, 235, 0.08) !important;
      }
    }
  }
}

.ant-btn-primary {
  background: #0c70eb;
  border-radius: 4px;
  color: #fff !important;
  border: none;
}

.ant-btn-primary:hover {
  background: #509fff !important;
}

.ant-btn-primary:focus {
  background: #0c70eb !important;
}

.ant-switch-checked {
  background-color: #0c70eb;
}
</style>