.searchInfo {
    padding: 24px 0 0 0;
    margin-left: -20px;

    .vocationPull {
        background: #FFFFFF;
        margin: 0 40px;
        display: flex;
        align-items: center;
        border-top-left-radius: 5px;
        border-bottom-left-radius: 5px;
        border-top-right-radius: 5px;
        border-bottom-right-radius: 5px;
    }

    .inputClass {
        width: 100%;
        height: 100%;
    }

    .line {
        width: 1px;
        height: 26px;
        background: #EFF0F4;
    }

    .lines {
        width: 1px;
        height: 56px;
        background: #EFF0F4;
    }

    .seekInfo {
        background: linear-gradient(270deg, #0142FD 0%, #2475F9 100%);
        font-weight: bold;
        font-size: 20px;
        color: #FFFFFF;
        display: flex;
        align-items: center;
        width: 160px;
        height: 56px;
        cursor: pointer;
        border-top-right-radius: 5px;
        border-bottom-right-radius: 5px;

        img {
            width: 24px;
            height: 24px;
            margin-left: 20px;
            margin-right: 8px;
        }
    }

    .stage {
        margin-left: 44px;
        margin-top: 16px;
    }
}

.tabContent {
    margin: 20px 32px 32px 20px;
    // background: #F5F7FC;
    border-radius: 10px;
    display: flex;
    align-items: flex-start;
    justify-content: space-around;
    position: relative;
    height: 560px;

    .title {
        font-size: 16px;
        font-weight: bold;
        color: #000000D9;
        line-height: 22px;
    }

    .substep {
        font-size: 14px;
        color: #00060E;
        line-height: 22px;
    }

    .hint {
        font-size: 14px;
        color: #A2ABB5FF;
        line-height: 22px;

        &::before {
            content: "*";
            color: red;
        }
    }

    .loading {
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
    }

    .tabModel {
        border-right: 1px solid #DAE2F5;
        height: 850px;
        overflow: hidden auto;
        flex: none
    }

    .tabModel::-webkit-scrollbar {
        display: none;
    }

    .cardContent {
        // height: 780px;
        overflow: hidden auto;
        position: relative;
    }

    .cardContent::-webkit-scrollbar {
        display: none;
    }

    .tab_text {
        font-size: 18px;
        color: #262626;
        cursor: pointer;
        height: 40px;
    }

    .activeTab {
        font-weight: bold;
        font-size: 18px;
        color: #FFFFFF;
        background-image: url("@/assets/images/home/<USER>");
        background-repeat: no-repeat;
        background-size: contain;
        width: 226px;
        height: 104px;
        line-height: 40px;
        padding-top: 32px;
    }

    .topTab {
        margin-top: 0;
        background-image: url("@/assets/images/home/<USER>");
        font-weight: bold;
        font-size: 18px;
        color: #FFFFFF;
        background-repeat: no-repeat;
        background-size: contain;
        width: 226px;
        height: 104px;
        line-height: 40px;
    }

    :deep(.ant-tag) {
        font-weight: 500;
        font-size: 12px;
        color: #2E7FFF;
        height: 20px;
        display: flex;
        align-items: center;
    }

    .card_total {
        display: flex;
        flex-wrap: wrap;
        align-items: flex-start;
        // min-height: 500px;
        // max-height: 700px;
        height: 500px;
        // border-bottom: 1px solid #DAE2F5;
    }

    .emptyPhoto {
        margin: auto;

        img {
            width: 240px;
            height: 248px;
        }
    }

    .card_content {
        // display: inline-block;
        position: relative;
        background: #F5F7FC;
        width: 49%;
        height: 163px;
        margin-bottom: 16px;
        // border-right: 1px solid #DAE2F5;
        // border-bottom: 1px solid #DAE2F5;
        cursor: pointer;
        border-radius: 10px;
        border: 2px solid transparent;
    }

    .selectBorder {
        border: 2px solid #0C70EB !important;
    }

    .rightActive {
        // border-right: none;
        margin-left: 16px;
    }

    .cardActive {
        background-color: #FFFFFF;
        transition: all 0.2s;
        box-shadow: 0 0 10px #DAE2F5;
    }

    .bottomLine {
        border-bottom: none;
    }

    .cardObvious {
        border-bottom: none;
    }

    .card_center {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        height: 117px;
        margin-left: 12px;
        width: 80%
    }

    .card_text {
        display: flex;
        align-items: center;
        justify-content: space-between;

        // .card_tag {
        // display: flex;
        // align-items: center;
        // }

        .cardTag {
            display: inline-block;
            padding-left: 8px;
            padding-right: 8px;
            margin-right: 8px;
        }

        .card_title {
            width: 200px;
            font-weight: bold;
            font-size: 16px;
            color: rgba(0, 0, 0, 0.85);
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 1;
            line-clamp: 1;
            margin-bottom: 4px;
        }

        :deep(.cityStyle) {
            font-size: 12px;
            color: rgba(0, 0, 0, 0.45);
            background-color: transparent;
            // margin-right: -6px;
        }
    }

    .card_des {
        font-size: 14px;
        color: rgba(0, 0, 0, 0.65);
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        line-clamp: 2;
        /* 控制显示的行数 */
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .layPage {
        width: 100%;
        text-align: center;
        margin: 15px 0;
    }
}

:deep(.ant-image-img) {
    height: 117px !important;
}

.btn_box {
    margin: 26px auto;
    font-weight: 500;
    font-size: 16px;
    line-height: 22px;
    text-align: right;
    display: flex;
    justify-content: space-between;

    .pase {
        font-size: 16px;
        color: #0C70EB;
        line-height: 19px;
        cursor: pointer;
    }

    .refuse {
        background: rgba(12, 112, 235, 0.08);
        border-radius: 4px 4px 4px 4px;
        color: #0C70EB;
        padding: 9px 24px;
        margin-right: 24px;
        cursor: pointer;
    }

    .submit {
        background: linear-gradient(270deg, #0142FD 0%, #2475F9 100%);
        border-radius: 4px 4px 4px 4px;
        color: #FFFFFF;
        padding: 9px 24px;
        cursor: pointer;
    }
}

:deep(.ant-radio-group) {
    display: flex !important;
    justify-content: center !important;
    margin-right: 6px !important;
    align-items: center !important;
}

:deep(.ant-checkbox-group) {
    display: flex !important;
    justify-content: center !important;
    margin-right: 6px !important;
    align-items: center !important;
}

:deep(.ant-form-item-label) {
    width: 100px;
    text-align: left !important;
}