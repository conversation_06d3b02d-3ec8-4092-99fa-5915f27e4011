<template>
  <div class="search-wrapper search-input-wrap">
    <a-input v-model:value="searchForm.ecopartnerName" placeholder="请输入公司名称或联系人姓名" allowClear style="width: 240px"
      @change="handleInputChange" />
    <div class="score-range-wrapper">
      <a-input-number v-model:value="searchForm.minScore" class="score-input" placeholder="最低生态评分" :min="0" :max="2000"
        :precision="1" @change="handleScoreChange" />
      <span>-</span>
      <a-input-number v-model:value="searchForm.maxScore" class="score-input" placeholder="最高生态评分" :min="0" :max="2000"
        :precision="1" @change="handleScoreChange" />
    </div>
    <div class="button-group">
      <a-button class="custom_btn active_btn" @click="handleSearch">搜索</a-button>
      <a-button class="custom_btn cancel_btn" @click="handleReset">重置</a-button>
    </div>
  </div>
</template>

<script>
import { defineComponent, reactive, watch } from 'vue'
import { message } from 'ant-design-vue'

export default defineComponent({
  name: 'EcoPartnerSearchForm',
  props: {
    modelValue: {
      type: Object,
      default: () => ({
        ecopartnerName: '',
        minScore: '',
        maxScore: ''
      })
    }
  },
  emits: ['update:modelValue', 'search', 'reset'],
  setup(props, { emit }) {
    const searchForm = reactive({
      ecopartnerName: props.modelValue.ecopartnerName || '',
      minScore: props.modelValue.minScore || '',
      maxScore: props.modelValue.maxScore || ''
    })

    // 监听props变化
    watch(() => props.modelValue, (newVal) => {
      Object.assign(searchForm, newVal)
    }, { deep: true })

    // 监听表单变化并同步到父组件
    watch(searchForm, (newVal) => {
      emit('update:modelValue', { ...newVal })
    }, { deep: true })

    // 验证评分范围
    const validateScoreRange = () => {
      const { minScore, maxScore } = searchForm
      if (minScore !== '' && maxScore !== '') {
        const min = Number(minScore)
        const max = Number(maxScore)
        if (min > max) {
          message.warning('最低生态评分不能大于最高生态评分')
          searchForm.maxScore = ''
          return false
        }
      }
      return true
    }

    const handleInputChange = () => {
      // 输入变化时的处理逻辑
    }

    const handleScoreChange = () => {
      validateScoreRange()
    }

    const handleSearch = () => {
      if (!validateScoreRange()) {
        return
      }
      emit('search', { ...searchForm })
    }

    const handleReset = () => {
      searchForm.ecopartnerName = ''
      searchForm.minScore = ''
      searchForm.maxScore = ''
      emit('reset')
    }

    return {
      searchForm,
      handleInputChange,
      handleScoreChange,
      handleSearch,
      handleReset
    }
  }
})
</script>

<style lang="scss" scoped>
.search-wrapper {
  margin-bottom: 16px;
  display: flex;
  flex-direction: row;
  gap: 20px;
  padding: 0 50px;
  margin-top: 10px;
}

.search-input-wrap {
  margin-bottom: 16px;
  display: flex;
  flex-direction: row;
  gap: 20px;
  padding: 0 50px;
  margin-top: 10px;
}

.score-range-wrapper {
  background-color: #fff;
  background-image: none;
  border: 1px solid #d9d9d9;
  display: flex;
  align-items: center;

  :deep(.score-input) {
    width: 130px;
    border: none;

    input {
      text-align: center;
    }
  }

  :deep(.score-input.ant-input-number-focused) {
    box-shadow: none;
    border: none;
  }

  span {
    padding: 0 8px;
    color: #999;
  }
}

.button-group {
  display: flex;
  gap: 8px;
}

.custom_btn {
  color: #FFFFFF;
  border-radius: 4px 4px 4px 4px;
  border: none;
}


.active_btn {
  background: linear-gradient(270deg, #0142FD 0%, #2475F9 100%);
}

.cancel_btn {
  background: rgba(12, 112, 235, 0.08);
  color: #0C70EB;
}

.custom_btn:hover,
.custom_btn:focus {
  opacity: 0.6;
}
</style>
