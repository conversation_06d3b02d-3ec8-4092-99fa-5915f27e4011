<template>
  <div>
    <div class="module_group" style="position: relative;">
      <a-button class="custom_btn active_btn" style="position:absolute;right:24px;top:24px;z-index:1;"
        @click="showNote = true">记录</a-button>
      <a-timeline v-if="orderComments.length > 0">
        <template v-for="item in orderComments" :key="item.yearMonth">
          <a-timeline-item color="blue">
            <template #dot>
              <img width="20px" height="20px" style="margin-bottom: 0;"
                src="@/assets/images/dispatchCenter/timeline.png" />
            </template>
            {{ item.yearMonth }}
          </a-timeline-item>
          <a-timeline-item v-for="(child, childIndex) in item.items" :key="child.id">
            <template #dot>
              <div class="customDot"></div>
            </template>
            <div class="flex just-between align-center">
              <span style="min-width: 100px;">{{ child.formattedTime }}</span>
              <span v-if="!child.edit" class="font_00060e" style="width:100%">{{ child.content }}</span>
              <a-textarea v-else :id="'inputRef' + item.yearMonth + child.id" v-model:value="child.content"
                style="border-radius: 8px;width:100%;" @blur="handleInputBlur(child)" :rows="7" :showCount="true"
                :maxlength="200" placeholder="请输入流程备忘录，限制200个字符"></a-textarea>
              <a-button type="link"
                @click="handleEditNote(child, ('inputRef' + item.yearMonth + child.id))">编辑</a-button>
              <a-popconfirm title="确定删除该数据?" ok-text="是" cancel-text="否"
                @confirm="handleDeleteNote(item.items, childIndex)">
                <a-button type="link" danger>删除</a-button>
              </a-popconfirm>
            </div>
          </a-timeline-item>
        </template>
      </a-timeline>
      <div v-else style="text-align: center;">暂无记录</div>
    </div>
    <a-modal v-model:visible="showNote" title="流程备忘录" :footer="null" @close="showNote = false" width="50%">
      <a-textarea :rows="7" :showCount="true" :maxlength="200" placeholder="请输入流程备忘录，限制200个字符"
        v-model:value="noteContent"></a-textarea>
      <div style="width: 100%; text-align: center; margin-top: 24px">
        <a-button class="margin_r_10" @click="handleNoteCancel"> 取消 </a-button>
        <a-button type="primary" @click="handleNoteConfirm"> 确认 </a-button>
      </div>
    </a-modal>
  </div>
</template>

<script>
import { defineComponent, reactive, toRefs, nextTick, onMounted, watch } from "vue";
import { message } from "ant-design-vue";
import {
  selectOrderComments,
  addOrderComment,
  updateOrderComment,
  deleteOrderComment
} from "@/api/processManage/index.js";
import { groupDataByMonthWithStats } from '@/utils/index.js';

export default defineComponent({
  props: {
    orderId: {
      type: [String, Number],
      required: true
    },
    orderComments: {
      type: Array,
      default: () => [],
    }
  },
  emits: ['update:orderComments'],
  setup(props, { emit }) {
    const data = reactive({
      showNote: false,
      noteContent: "",
    });

    const queryOrderComments = (orderId) => {
      selectOrderComments(orderId).then((res) => {
        if (res.code === 200) {
          emit('update:orderComments', groupDataByMonthWithStats(res.data));
        }
      });
    };

    const handleInputBlur = (child) => {
      child.edit = false;
      // 去除首尾空格
      const trimmedContent = child.content.trim();
      if (!trimmedContent) {
        // 内容为空时，恢复到原始内容并提示
        message.warning("内容不能为空");
        child.content = child.contentCopy;
        return;
      } else {
        // 更新为去除空格后的内容
        child.content = trimmedContent;
        // 调用API更新
        updateOrderComment({ id: child.id, comment: child.content }).then(() => {
          queryOrderComments(props.orderId);
        });
      }
    };

    const handleDeleteNote = (items, index) => {
      deleteOrderComment(items[index].id).then(() => {
        message.success("删除成功");
        items.splice(index, 1);
      }).catch(() => {
        message.error("删除失败");
      });
    };

    const handleEditNote = (child, refInput) => {
      // 在开始编辑时备份当前内容
      child.contentCopy = child.content;
      child.edit = true;
      if (child.edit) {
        nextTick(() => {
          const input = document.querySelector(`#${refInput}`);
          if (input) {
            input.focus();
          }
        });
      }
    };

    const handleNoteConfirm = () => {
      if (data.noteContent.trim() !== "") {
        addOrderComment({
          orderId: props.orderId,
          comment: data.noteContent,
        }).then((res) => {
          message.success(res.msg);
          queryOrderComments(props.orderId);
          data.showNote = false;
          data.noteContent = "";
        });
      } else {
        message.warning("请填写备忘录内容");
      }
    };

    const handleNoteCancel = () => {
      data.showNote = false;
      data.noteContent = "";
    };
    const initContentBackup = () => {
      if (props.orderComments && Array.isArray(props.orderComments)) {
        props.orderComments.forEach(item => {
          if (item.items && Array.isArray(item.items)) {
            item.items.forEach(child => {
              if (child.content && !child.contentCopy) {
                child.contentCopy = child.content;
              }
            });
          }
        });
      }
    };

    // 监听 orderComments 变化，确保新数据也有备份
    watch(() => props.orderComments, () => {
      initContentBackup();
    }, { deep: true, immediate: true });

    onMounted(() => {
      initContentBackup();
    });
    return {
      ...toRefs(data),
      handleInputBlur,
      handleDeleteNote,
      handleEditNote,
      handleNoteConfirm,
      handleNoteCancel
    };
  }
});
</script>

<style scoped>
.customDot {
  width: 8px;
  height: 8px;
  background: #0C70EB;
  border-radius: 50%;
}

.module_group {
  padding: 24px;
  margin: 0 24px 0 32px;
  border-radius: 8px 8px 8px 8px;
  border: 1px solid #D1D0D8;
}

.module_group:not(:first-child) {
  margin-top: 20px;
}

.custom_btn {
  color: #FFFFFF;
  border-radius: 4px 4px 4px 4px;
  border: none;
}

.active_btn {
  background: linear-gradient(270deg, #0142FD 0%, #2475F9 100%);
}

.custom_btn:hover,
.custom_btn:focus {
  opacity: 0.6;
}

:deep(.ant-timeline .ant-timeline-item:last-of-type .ant-timeline-item-tail) {
  display: none;
}
</style>