<template>
  <el-table v-if="contactData && contactData.length > 0" :data="contactData" :empty-text="'暂无数据'"
    class="resize-table-header-line" :cell-style="{ textAlign: 'center' }" :header-cell-style="{ textAlign: 'center' }"
    border style="width: 100%; margin-top: 20px; margin-bottom: 20px">
    <el-table-column prop="ownProvince" label="自有联系人">
      <template #default="scope">
        <div class="contact-list">
          <div v-for="(contactItem, index) in scope.row.ownProvince" :key="index" class="box person-wrap">
            <div class="contact-selection">
              <!-- 单选模式 -->
              <a-radio-group v-if="selectionMode === 'single'" :value="selectedValue">
                <a-radio :value="getContactValue(contactItem)" @change="(e) => handleSelectionChange(e, contactItem)"
                  :disabled="isContactDisabled(contactItem)" />
              </a-radio-group>
              <!-- 多选模式 -->
              <a-checkbox v-else :checked="isSelected(contactItem)"
                @change="(e) => handleSelectionChange(e, contactItem)" :disabled="isContactDisabled(contactItem)" />
              <span class="font-weight-500">
                {{ contactItem.belong }}
              </span>
            </div>
            <span class="contactName">
              {{ contactItem.contactName }}
            </span>
            <p class="contactPhone">
              {{ contactItem.contactPhone }}
            </p>
          </div>
          <div v-if="scope.row.ownProvince.length === 0" class="no-data">
            暂无数据
          </div>
        </div>
      </template>
    </el-table-column>
  </el-table>
</template>

<script>
import { defineComponent } from 'vue'
import { isSamePerson } from '@/utils/index.js';

export default defineComponent({
  name: 'OwnContactSelector',
  props: {
    // 联系人数据
    contactData: {
      type: Array,
      default: () => []
    },
    // 选择模式 (single: 单选, multiple: 多选)
    selectionMode: {
      type: String,
      default: 'single',
      validator: (value) => ['single', 'multiple'].includes(value)
    },
    // 单选模式下的选中值
    selectedValue: {
      type: String,
      default: ''
    },
    // 多选模式下的选中值数组
    selectedValues: {
      type: Array,
      default: () => []
    },
    // 被拒绝的联系人ID列表
    rejectCompanyIdList: {
      type: Array,
      default: () => []
    },
    // 选择值的字段名 (contactPhone, belong 等)
    valueField: {
      type: String,
      default: 'belong'
    },
    // 已选择的自有能力方数据（用于同一人检查）
    selectedOwnPersons: {
      type: Array,
      default: () => []
    }
  },
  emits: ['selection-change', 'limit-exceeded'],
  setup(props, { emit }) {
    // 获取联系人的选择值
    const getContactValue = (contact) => {
      return contact.contactPhone
    }
    // 检查是否已选中
    const isSelected = (contact) => {
      const value = getContactValue(contact)
      return props.selectedValues.includes(value)
    }
    // 检查联系人是否被禁用
    const isContactDisabled = (contact) => {
      // 检查是否在拒绝列表中
      const isRejected = props.rejectCompanyIdList.some(
        (value) => value.userId === contact.userId
      );
      // 检查是否与已选择的自有能力方为同一人
      // const isDuplicateWithOwnPerson = props.selectedOwnPersons.some(
      //   (person) => isSamePerson(contact, person)
      // );
      return isRejected;
    }

    // 处理选择变化
    const handleSelectionChange = (e, contact) => {
      if (props.selectionMode === 'single') {
        // 单选模式：检查是否与已选择的自有能力方为同一人
        const isDuplicateWithOwnPerson = props.selectedOwnPersons.some(
          (person) => isSamePerson(contact, person)
        );
        if (isDuplicateWithOwnPerson) {
          emit('limit-exceeded', '自有能力方和自有联系人不可以是同一方')
          return // 不传递事件到父组件
        }
        // 没有重复，传递事件到父组件
        emit('selection-change', e, contact)
      } else {
        // 多选模式：检查是否与已选择的自有能力方为同一人
        const { checked } = e.target
        if (checked) {
          const isDuplicateWithOwnPerson = props.selectedOwnPersons.some(
            (person) => isSamePerson(contact, person)
          );
          if (isDuplicateWithOwnPerson) {
            // 多选模式下，取消复选框选中状态
            e.target.checked = false;
            emit('limit-exceeded', '自有能力方和自有联系人不可以是同一方')
            return // 不传递事件到父组件
          }
        }
        // 没有重复，传递事件到父组件
        emit('selection-change', e, contact)
      }
    }

    return {
      getContactValue,
      isSelected,
      isContactDisabled,
      handleSelectionChange
    }
  }
})
</script>

<style lang="scss" scoped>
.contact-list {
  padding: 12px 0;
}

.box.person-wrap {
  width: max-content;
  min-width: 100%;
  padding: 0 38px;
  text-align: left;
  display: flex;
  align-items: center;
  gap: 5px;
  margin-bottom: 26px;
  white-space: nowrap;
  justify-content: space-between;

  &:last-of-type {
    margin-bottom: 0;
  }
}

.contact-selection {
  display: flex;
  width: 200px;
  align-items: center;
  gap: 8px;
}

.font-weight-500 {
  font-weight: 500;
}

.contactName {
  min-width: 140px;
  text-align: left;
  display: inline-block;
  margin-bottom: 0;
  color: rgba(0, 0, 0, 0.85);
  border: 1px solid #d9d9d9;
  border-radius: 2px;
  transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
  height: 32px;
  padding: 0 11px;
  line-height: 30px;
}

.contactPhone {
  margin: 0;
  min-width: 120px;
  text-align: left;
  color: #666;
}

.no-data {
  text-align: center;
  color: #999;
  padding: 20px;
}
</style>
