<template>
  <el-table v-if="ownPersonData && ownPersonData.length > 0" :data="ownPersonData" :empty-text="'暂无数据'"
    class="resize-table-header-line" :cell-style="{ textAlign: 'center' }" :header-cell-style="{ textAlign: 'center' }"
    border style="width: 100%; margin-top: 20px; margin-bottom: 20px">
    <el-table-column v-if="!ecologyType" prop="ownPerson" label="自有能力方" />
    <el-table-column v-if="ecologyType && ecologyType.includes('1')" prop="ownPerson" label="自有能力方">
      <template #default="scope">
        <div class="own-person-list">
          <div v-for="(ownPersonItem, index) in scope.row.ownPerson" :key="index" class="box person-wrap">
            <div class="person-selection">
              <!-- 单选模式 -->
              <a-radio-group v-if="selectionMode === 'single'" :value="selectedValue">
                <a-radio :value="ownPersonItem.contactPhone" @change="(e) => handleSelectionChange(e, ownPersonItem)"
                  :disabled="isPersonDisabled(ownPersonItem)" />
              </a-radio-group>
              <!-- 多选模式 -->
              <a-checkbox v-else :checked="isSelected(ownPersonItem)"
                @change="(e) => handleSelectionChange(e, ownPersonItem)" :disabled="isPersonDisabled(ownPersonItem)" />
              <span class="font-weight-500">
                {{ ownPersonItem.belong }}
              </span>
            </div>
            <span class="contactName">
              {{ ownPersonItem.contactName }}
            </span>
            <p class="contactPhone">
              {{ ownPersonItem.contactPhone }}
            </p>
          </div>
          <div v-if="scope.row.ownPerson.length === 0" class="no-data">
            暂无数据
          </div>
        </div>
      </template>
    </el-table-column>
  </el-table>
</template>

<script>
import { defineComponent } from 'vue'
import { isSamePerson } from '@/utils/index.js';

export default defineComponent({
  name: 'OwnCapabilitySelector',
  props: {
    // 自有能力方数据
    ownPersonData: {
      type: Array,
      default: () => []
    },
    // 生态类型
    ecologyType: {
      type: String,
      default: ''
    },
    // 选择模式 (single: 单选, multiple: 多选)
    selectionMode: {
      type: String,
      default: 'single',
      validator: (value) => ['single', 'multiple'].includes(value)
    },
    // 单选模式下的选中值
    selectedValue: {
      type: String,
      default: ''
    },
    // 多选模式下的选中值数组
    selectedValues: {
      type: Array,
      default: () => []
    },
    // 被拒绝的公司ID列表
    rejectCompanyIdList: {
      type: Array,
      default: () => []
    },
    // 已选择的自有联系人数据（用于同一人检查）
    selectedContactPersons: {
      type: Array,
      default: () => []
    }
  },
  emits: ['selection-change', 'limit-exceeded'],
  setup(props, { emit }) {
    // 检查是否已选中
    const isSelected = (person) => {
      const value = person.contactPhone
      return props.selectedValues.includes(value)
    }
    const isPersonDisabled = (person) => {
      // 检查是否在拒绝列表中
      const isRejected = props.rejectCompanyIdList.some(
        (value) => value.userId === person.userId
      );
      // 检查是否与已选择的自有联系人为同一人
      // const isDuplicateWithContact = props.selectedContactPersons.some(
      //   (contact) => isSamePerson(person, contact)
      // );
      return isRejected;
    }

    // 处理选择变化
    const handleSelectionChange = (e, person) => {
      if (props.selectionMode === 'single') {
        // 单选模式：检查是否与已选择的自有联系人为同一人
        const isDuplicateWithContact = props.selectedContactPersons.some(
          (contact) => isSamePerson(person, contact)
        );
        if (isDuplicateWithContact) {
          emit('limit-exceeded', '自有能力方和自有联系人不可以是同一方')
          return // 不传递事件到父组件
        }
        // 没有重复，传递事件到父组件
        emit('selection-change', e, person)
      } else {
        // 多选模式：检查是否与已选择的自有联系人为同一人
        const { checked } = e.target
        if (checked) {
          const isDuplicateWithContact = props.selectedContactPersons.some(
            (contact) => isSamePerson(person, contact)
          );
          if (isDuplicateWithContact) {
            // 多选模式下，取消复选框选中状态
            e.target.checked = false;
            emit('limit-exceeded', '自有能力方和自有联系人不可以是同一方')
            return // 不传递事件到父组件
          }
        }
        // 没有重复，传递事件到父组件
        emit('selection-change', e, person)
      }
    }

    return {
      isSelected,
      isPersonDisabled,
      handleSelectionChange
    }
  }
})
</script>

<style lang="scss" scoped>
.own-person-list {
  padding: 12px 0;
}

.box.person-wrap {
  width: max-content;
  min-width: 100%;
  padding: 0 38px;
  text-align: left;
  display: flex;
  align-items: center;
  gap: 5px;
  margin-bottom: 26px;
  white-space: nowrap;
  justify-content: space-between;

  &:last-of-type {
    margin-bottom: 0;
  }
}

.person-selection {
  display: flex;
  width: 200px;
  align-items: center;
  gap: 8px;
}

.font-weight-500 {
  font-weight: 500;
}

.contactName {
  min-width: 140px;
  text-align: left;
  display: inline-block;
  margin-bottom: 0;
  color: rgba(0, 0, 0, 0.85);
  border: 1px solid #d9d9d9;
  border-radius: 2px;
  transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
  height: 32px;
  padding: 0 11px;
  line-height: 30px;
}

.contactPhone {
  margin: 0;
  min-width: 120px;
  text-align: left;
  color: #666;
}

.no-data {
  text-align: center;
  color: #999;
  padding: 20px;
}
</style>
