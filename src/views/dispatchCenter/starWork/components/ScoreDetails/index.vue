<template>
    <a-descriptions style="margin-top: 10px" v-for="(textItem, index) in textList" :key="index" bordered size="small"
        :column="2">
        <a-descriptions-item label="支撑方" :span="2">
            {{ textItem?.company }}&nbsp;&nbsp;{{ textItem?.contactUserName }}&nbsp;&nbsp;{{ textItem?.contactPhone }}
        </a-descriptions-item>
        

        <a-descriptions-item label="是否支撑">
            {{ actionIsSupport(textItem) }}
        </a-descriptions-item>

        <a-descriptions-item v-if="textItem?.dealType == '1'" label="支撑人天">
            {{ textItem?.allParseData.supportCos }}
        </a-descriptions-item>

        <a-descriptions-item v-if="textItem?.dealType == '1'" label="实际支撑结果">
            {{ textItem?.allParseData.suggest }}
        </a-descriptions-item>

        <a-descriptions-item label="回复时间">{{ textItem?.dealTime }}</a-descriptions-item>

        <a-descriptions-item v-if="textItem?.dealType == '2' || textItem?.dealType == '3'" label="拒绝原因" :span="2">
            {{ formatDealContent(textItem) }}
        </a-descriptions-item>

        <a-descriptions-item v-if="textItem?.dealType == '1' && textItem?.allParseData.fileListDeal.length != 0"
            label="附件" :span="2">
            <div class="file-list" v-if="textItem?.allParseData.fileListDeal.length != 0">
                <div class="flex">
                    <p>
                        <span>
                            <i class="iconfont icon-annex"></i>
                            <span>&nbsp;{{ textItem.allParseData.fileListDeal[0]?.name }}&nbsp;</span>
                        </span>
                    </p>
                    <div class="font_0c70eb">
                        <span @click="viewFile(textItem.allParseData.fileListDeal[0])">&nbsp;预览</span>
                        <span @click="downloadFile(textItem.allParseData.fileListDeal[0])">&nbsp;下载</span>
                    </div>
                </div>
            </div>
        </a-descriptions-item>

        <!-- enterpriseId为null是自有能力方或自有联系人 -->
        <a-descriptions-item
            v-if="textItem?.enterpriseId && (textItem?.dealType == '1' || (textItem?.dealType == '3' && textItem?.hasSupported))">
            <template #label>
                <div style=" display: flex; ">
                    <span>支撑满意度111</span>
                    <el-tooltip effect="dark" content="请对售前支撑整体满意度打分，非常满意(8-10]分，满意(6-8]分，一般(4-6]，不满意(0,4]分）"
                        placement="top">
                        <div class="header-help-icon">
                            <el-icon :size="12">
                                <QuestionFilled />
                            </el-icon>
                        </div>
                    </el-tooltip>
                </div>
            </template>
            <template v-if="status === 'writeScore' && !textItem.scored">
                <el-input-number v-model="textItem.satisfiedScore" :min="0.1" :max="10" :step="1" />
            </template>
            <template v-else>
                <span>{{ textItem?.satisfiedScore }}</span>
            </template>
        </a-descriptions-item>

        <a-descriptions-item v-if="textItem.enterpriseId&&(textItem?.dealType == '1' || (textItem?.dealType == '3' && textItem?.hasSupported))">
            <template #label>
                <div style="display: flex">
                    <span>售前响应及时率</span>
                    <el-tooltip effect="dark" content="请对售前支撑响应及时率的满意度情况打分，非常满意(8-10]分，满意(6-8]分，一般(4-6]，不满意(0,4]分）"
                        placement="top">
                        <div class="header-help-icon">
                            <el-icon :size="12">
                                <QuestionFilled />
                            </el-icon>
                        </div>
                    </el-tooltip>
                </div>
            </template>
            <template v-if="status === 'writeScore' && !textItem.scored">
                <el-input-number v-model="textItem.responseScore" :min="0.1" :max="10" :step="1" />
            </template>
            <template v-else>
                <span>{{ textItem?.responseScore }}</span>
            </template>
        </a-descriptions-item>

        <a-descriptions-item v-if="textItem?.dealType == '1' || textItem?.dealType == '3'" label="生态评价" :span="2">
            <el-input v-if="status === 'writeScore' && !textItem.scored" v-model="textItem.suggest" type="textarea"
                placeholder="请输入生态评价" :rows="4" :cols="50" :maxlength="100" :show-word-limit="true" />
            <template v-else>
                <span>{{ textItem?.comment }}</span>
            </template>
        </a-descriptions-item>
    </a-descriptions>
</template>

<script>
import { defineComponent } from "vue";
import { QuestionFilled } from "@element-plus/icons-vue";

export default defineComponent({
    name: "ScoreDetails",
    props: {
        textList: {
            type: Array,
            required: true,
        },
        status: {
            type: String,
            default: "",
        },
    },
    emits: [
        'view',
        'download',
    ],
    components: {
        QuestionFilled,
    },
    setup(props, { emit }) {
        /**
         * 判断是否支撑的文本
         */
        const actionIsSupport = (item) => {
            if (!item) return "";
            if (item.dealType === "1") {
                return "同意";
            } else if (item.dealType === "2") {
                return "拒绝";
            } else if (item.dealType === "3") {
                return "超时拒绝";
            }
            return "";
        };

        /**
         * 格式化拒绝内容
         */
        const formatDealContent = (textItem) => {
            if (textItem.dealContent === "其他") {
                return textItem?.comment || textItem.dealContent;
            }
            return textItem?.dealContent || "-";
        };

        /**
         * 文件预览
         */
        const viewFile = (file) => {
            emit("view", file);
        };

        /**
         * 文件下载
         */
        const downloadFile = (file) => {
            emit("download", file);
        };

        return {
            actionIsSupport,
            formatDealContent,
            viewFile,
            downloadFile,
        };
    },
});
</script>
<style lang="scss" scoped>
.file-list {
    width: 40%!important;
    cursor: pointer;

    p {
        position: relative;
        padding: 2px 24px 2px 24px;
        width: 80%;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        margin-bottom: 8px;
        background: rgba(12, 112, 235, 0.08);
        border-radius: 4px;
    }

    .iconSize {
        width: 15px;
        height: 15px;
    }

    .close {
        position: absolute;
        right: 8px;
        top: 7px;
    }

    .icon-annex {
        width: 13px;
        height: 13px;
        color: #0c70eb;
        position: absolute;
        top: 2px;
        left: 8px;
    }
}

.font_0c70eb {
    width: 80px; // 解决附件操作文本不正常换行问题
    color: #0c70eb;
}
</style>
