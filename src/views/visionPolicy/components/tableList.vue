<template>
  <div class="topBac">
    <div class="policyText">政策解读</div>
    <div class="vocationPull" style="flex: 1; height: 56px">
      <a-input
        v-model:value="name"
        class="inputClass"
        allow-clear
        @keyup.enter="seekContent"
        height="56px"
        placeholder="请输入关键词进行搜索"
      />
      <div class="seekInfo commonBtn" @click="seekContent">
        <img src="@/assets/images/home/<USER>" />
        <div>搜索</div>
      </div>
    </div>
    <div class="selectData" ref="selectData">
      <div
        :class="[
          'selcet_box',
          { showMore: showScense == 'label' && showIndex === index },
        ]"
        v-for="(val, index) in tradeList"
        :key="index"
      >
        <div class="left_select">{{ val.label }}：</div>
        <div
          :class="[
            'right_select',
            { showHidden: showScense == 'label' && showIndex === index },
          ]"
        >
          <span
            ref="box"
            v-if="val.label == '行业'"
            v-for="(value, key1) in val.children"
            :key="key1"
            :class="{ activeBtn: activeKey === value.value }"
            style="height: 44px"
          >
            <div class="title" @click="providerBtn(value)">
              {{ value.label }}
            </div>
          </span>
        </div>
        <span
          class="more flex"
          v-if="val.children && val.children.length > 8 && showIndex !== index"
          @click="showMore('label', index)"
          >更多<img src="@/assets/images/visionPolicy/downArrow.png" alt=""
        /></span>
        <span
          class="more flex"
          v-if="val.children && val.children.length > 8 && showIndex === index"
          @click="showless('label_less', index)"
          >收起<img src="@/assets/images/visionPolicy/topArrow.png" alt=""
        /></span>
      </div>
    </div>
  </div>

  <div class="policyContain">
    <template v-for="(item, index) in policyList" :key="index">
      <div class="policyContent pointer" @click="goDetail(item)">
        <div class="leftContent">
          <div class="botNum">
            {{
              item.publishTime
                ? item.publishTime.slice(0, 7).replace("-", ".")
                : item.createTime.slice(0, 7).replace("-", ".")
            }}
          </div>
          <div class="topNum">
            {{
              item.publishTime
                ? item.publishTime.slice(0, 10).slice(-2)
                : item.createTime.slice(0, 10).slice(-2)
            }}
          </div>
        </div>

        <div class="rightContent">
          <div class="title flex align-center">
            {{ item.name }}
            <img
              src="@/assets/images/visionPolicy/hot.png"
              width="20px"
              height="20xp"
              style="margin-left: 13px"
              v-if="item.isHot == 1"
            />
          </div>
          <div class="lineHead"></div>
          <div class="desc" v-html="item.details"></div>
          <div class="botLine"></div>
        </div>
      </div>
    </template>
    <div v-if="policyList.length == 0" class="emptyPhoto">
      <img src="@/assets/images/home/<USER>" />
    </div>
    <div class="layPage" v-else>
      <a-pagination
        v-model:pageSize="pageItemSize"
        v-model:current="currentPage"
        :pageSizeOptions="pageSizeOptions"
        show-quick-jumper
        show-size-changer
        :total="totalItemCount"
        @change="pageChange"
        @showSizeChange="sizeChange"
        class="mypage"
      />
    </div>
    <div class="loading" v-show="loadingShow">
      <a-spin />
    </div>
  </div>

  <!-- <div class="gennralPolicy">
    <div class="text">通用政策解读</div>
  </div>

  <div class="readContent" v-if="currentItem">
    <div
      :class="[
        'animate__animated pointer',
        {
          animate__fadeInRight: rightAnimate,
          animate__fadeInLeft: leftAnimate,
        },
      ]"
      @click="goDetail(currentItem)"
    >
      <div class="text">{{ currentItem.name }}</div>
      <div class="date">
        发表日期：{{
          currentItem.publishTime
            ? timeDeal(currentItem.publishTime)
            : timeDeal(currentItem.createTime)
        }}
      </div>
      <div class="desc" v-html="currentItem.details"></div>
    </div>

    <div class="operation" v-if="commonPolicyList.length > 1">
      <a-button
        type="text"
        shape="circle"
        @click="showPrevious"
        :disabled="currentIndex == 0"
        style="
          width: 56px;
          height: 56px;
          background: linear-gradient(
            163deg,
            #f1f3f6 0%,
            #f6f7f9 38%,
            #ffffff 100%
          );
          box-shadow: 4px 4px 12px 0px rgba(0, 0, 0, 0.06),
            -2px -2px 8px 0px rgba(255, 255, 255, 0.5);
        "
      >
        <template #icon>
          <LeftOutlined style="color: rgba(46, 56, 82, 0.2); font-size: 20px" />
        </template>
      </a-button>

      <a-button
        type="text"
        shape="circle"
        @click="showNext"
        :disabled="currentIndex == commonPolicyList.length - 1"
        style="
          margin-left: 48px;
          width: 56px;
          height: 56px;
          background: linear-gradient(
            163deg,
            #f1f3f6 0%,
            #f6f7f9 38%,
            #ffffff 100%
          );
          box-shadow: 4px 4px 12px 0px rgba(0, 0, 0, 0.06),
            -2px -2px 8px 0px rgba(255, 255, 255, 0.5);
        "
      >
        <template #icon>
          <RightOutlined
            style="color: rgba(46, 56, 82, 0.2); font-size: 20px"
          />
        </template>
      </a-button>
    </div>
  </div>
  <div style="height: 30px" v-if="commonPolicyList.length > 0"></div>
  <div v-if="commonPolicyList.length == 0" class="emptyPhoto generalEmpty">
    <img src="@/assets/images/home/<USER>" />
  </div> -->
  <div style="height: 20px"></div>
</template>

<script>
import { defineComponent, reactive, toRefs, ref } from "vue";
import { getLabelTreeList } from "@/api/solutionNew/home";
import { LeftOutlined, RightOutlined } from "@ant-design/icons-vue";
import { visionList, allVisionList } from "@/api/visionPolicy/visionList";
import zhCN from "ant-design-vue/es/locale/zh_CN";
import { useRouter } from "vue-router";
import eventBus from "@/utils/eventBus";

export default defineComponent({
  components: {
    LeftOutlined,
    RightOutlined,
    zhCN,
  },
  setup() {
    const selectData = ref(null);
    const box = ref(null);
    const vocation = ref("");
    const Router = useRouter();
    const data = reactive({
      showMore: false,
      showScense: "",
      name: "",
      vocationList: [],
      currentPage: 1,
      pageItemSize: 10,
      totalItemCount: 0,
      pageSizeOptions: ["10", "20", "30", "50"],
      showLast: false,
      loadingShow: true,
      showId: undefined,
      activeKey: "",
      showIndex: "",
      rightAnimate: false,
      leftAnimate: false,
      providerSelect: [],
      selectListOld: [],
      selectListNew: [],
      policyList: [],
      commonPolicyList: [],
      currentIndex: 0,
      currentItem: {},
      tradeList: [],
    });

    const providerList = () => {
      getLabelTreeList().then((res) => {
        data.vocationList = res.data.map((item) => ({
          label: item.name,
          value: item.id,
          length: item.children ? item.children.length : 0,
          children: item.children
            ? item.children.map((child) => ({
                label: child.name,
                value: child.id,
                children: child.children
                  ? child.children.map((ele) => ({
                      label: ele.name,
                      value: ele.id,
                    }))
                  : undefined,
              }))
            : undefined,
        }));
        data.vocationList.forEach((item) => {
          if (item.label == "行业") {
            item.children = item.children.filter((item) => {
              if (item.value != 550 && item.value != 561) {
                return item;
              }
            });
            item.children.unshift({
              label: "全域",
              value: -1,
            });
            data.tradeList.push(item);
          }
        });
      });

      // let parmas = {
      //   shelfStatus: 1,
      //   industryId: -1,
      // };
      // allVisionList(parmas).then((res) => {
      //   data.commonPolicyList = res.data;
      //   data.currentItem = data.commonPolicyList[data.currentIndex];
      // });
    };
    providerList();

    const timeDeal = (value) => {
      if (value) {
        return value.slice(0, 10).replaceAll("-", ".");
      }
      return "-";
    };

    const goDetail = (val) => {
      Router.push({ name: "visionDetail", query: { id: val.id } });
    };

    const getVision = () => {
      let params = {
        name: data.name,
        pageNo: data.currentPage,
        pageSize: data.pageItemSize,
        shelfStatus: 1,
        industryId: data.showId,
      };
      data.loadingShow = true;
      visionList(params)
        .then((res) => {
          data.loadingShow = false;
          data.policyList = res.data.rows;
          data.totalItemCount = res.data.totalRows;
        })
        .catch((error) => {
          data.loadingShow = false;
        });
    };
    getVision();

    eventBus.on("getVisionList", getVision);

    const seekContent = () => {
      data.currentPage = 1;
      getVision();
    };

    const getBoxTitle = (key) => {
      const rect = box.value[key].getBoundingClientRect();
      const selectHtml = selectData.value.getBoundingClientRect();
      return {
        left: rect.x - selectHtml.x + "px",
        top: rect.y - selectHtml.y + "px",
      };
    };

    const providerBtn = (value) => {
      if (data.activeKey == value.value) {
        data.activeKey = null;
        data.showId = null;
      } else {
        data.activeKey = value.value;
        data.showId = value.value;
      }
      data.showScense = "label";
      data.currentPage = 1;
      getVision();
    };

    const showNext = async () => {
      if (data.rightAnimate) return;
      data.rightAnimate = true;
      if (data.currentIndex < data.commonPolicyList.length - 1) {
        data.currentIndex++;
        data.currentItem = data.commonPolicyList[data.currentIndex];
      }
      await new Promise((resolve) => {
        setTimeout(() => {
          data.rightAnimate = false;
          resolve();
        }, 1000);
      });
    };

    const showPrevious = async () => {
      if (data.leftAnimate) return;
      data.leftAnimate = true;
      if (data.currentIndex > 0) {
        data.currentIndex--;
        data.currentItem = data.commonPolicyList[data.currentIndex];
      }
      await new Promise((resolve) => {
        setTimeout(() => {
          data.leftAnimate = false;
          resolve();
        }, 1000);
      });
    };

    const pageChange = (page, pageSize) => {
      data.currentPage = page;
      getVision();
    };

    const sizeChange = (current, size) => {
      data.pageItemSize = size;
      getVision();
    };

    const getBoxLeft = (key) => {
      const rect = box.value[key].getBoundingClientRect();
      const firstRect = box.value[0].getBoundingClientRect();
      const selectHtml = selectData.value.getBoundingClientRect();
      return {
        left: firstRect.x - selectHtml.x + "px",
        top: rect.y - selectHtml.y + 49 + "px",
      };
    };

    const showMore = (type, index) => {
      if (type == "provider") {
        data.showScense = type;
        data.morePro = false;
      } else {
        data.showIndex = index;
        data.showScense = type;
      }
    };
    const providerLeave = (val, index) => {
      data.showId = "";
      data.showLast = false;
    };

    const showless = (type, index) => {
      if (type == "provider_less") {
        data.showScense = type;
        data.morePro = true;
      } else {
        data.showIndex = "";
        data.showScense = type;
      }
    };

    return {
      ...toRefs(data),
      box,
      goDetail,
      timeDeal,
      seekContent,
      sizeChange,
      showMore,
      showNext,
      showPrevious,
      getBoxLeft,
      getBoxTitle,
      providerLeave,
      showless,
      pageChange,
      providerBtn,
      selectData,
    };
  },
});
</script>

<style lang="scss" scoped src="./tableList.scss"></style>

<style lang="scss">
.ant-select:not(.ant-select-customize-input) .ant-select-selector {
  border: none;
}

:deep(.ant-cascader-input.ant-input) {
  border: none !important;
}

.mypage {
  .ant-pagination {
    color: rgba(0, 0, 0, 0.65);
  }

  .ant-select-selector {
    color: rgba(0, 0, 0, 0.65);
  }

  .ant-pagination-item-active {
    background: #007eff;
  }

  .ant-pagination-item-active a {
    color: #ffffff;
  }

  .ant-pagination-item-active:focus a,
  .ant-pagination-item-active:hover a {
    color: #ffffff;
  }

  .ant-pagination-item-active a:focus,
  .ant-pagination-item-active a:hover {
    color: #ffffff;
  }

  .ant-pagination-item:focus,
  .ant-pagination-item:hover {
    background: #007eff;
  }

  .ant-pagination-item:focus a,
  .ant-pagination-item:hover a {
    color: #ffffff;
  }

  .ant-pagination-item-disabled:hover,
  .ant-pagination-item-disabled:focus {
    background-color: #ffffff;
    border-color: #d9d9d9;

    a {
      color: rgba(0, 0, 0, 0.25);
    }
  }

  .ant-pagination-options-size-changer.ant-select {
    margin-right: 0;
  }

  .ant-pagination-options-quick-jumper {
    margin-left: 8px;
  }
}
</style>