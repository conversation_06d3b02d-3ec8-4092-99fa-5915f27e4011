<template>
  <div>
    <div class="searchInfo flex" :class="switchOnOff ? 'AIBg' : 'commonBg'">
      <div class="vocationPull" style="flex: 1; height: 56px">
        <div class="switch">
          <div class="AIlogo"></div>
          <a-switch
            checked-children="on"
            un-checked-children="off"
            v-model:checked="switchOnOff"
          />
        </div>
        <a-config-provider
          :locale="zhCN"
          :getPopupContainer="(triggerNode) => triggerNode.parentNode"
        >
          <div class="line"></div>

          <div class="lines"></div>
          <a-input
            v-model:value="name"
            class="inputClass"
            allow-clear
            height="56px"
            @keyup.enter="seekContent"
            placeholder="请输入场景名称、场景描述、核心功能等关键词进行搜索"
          />
          <voiceRecorder
            v-if="switchOnOff"
            :isTranslating="isTranslating"
            :canBtnUse="canBtnUse"
            @audioReady="handleAudio"
          />
          <div
            class="seekInfo"
            :class="switchOnOff ? 'AIbtn' : 'commonBtn'"
            @click="seekContent"
          >
            <img src="@/assets/images/home/<USER>" alt=""/>
            <div>搜索</div>
          </div>
        </a-config-provider>
      </div>
    </div>
    <div class="newLoading" v-if="AIsearch">
      <loadingSmall />
    </div>

    <div class="selectData" ref="selectData" v-if="true || !switchOnOff">
      <div class="selcet_box">
        <div class="left_select">场景分类：</div>
        <div class="right_select">
          <span
            ref="box"
            v-for="(value, key1) in tabClassifyList"
            :key="key1"
            :class="{ activeBtn: classify == value.value }"
            style="height: 49px"
          >
            <div
              class="title"
              @click="classifyBtn(value)"
              @mouseenter="providerEnter(value)"
            >
              {{ value.label }}
            </div>
          </span>
        </div>
      </div>
      <div
        v-for="(value, key1) in tabClassifyList"
        :key="key1"
        @mouseleave="providerLeave(value, key1)"
      >
        <div
          class="last_data_top"
          :style="getBoxTitle(key1)"
          v-if="showLast && showId == value.value"
          @click="classifyBtn(value)"
        >
          {{ value.label }}
        </div>
        <div
          class="last_data"
          v-if="showLast && showId == value.value"
          :style="getBoxLeft(key1)"
        >
          <span
            v-for="(e, i) in value.children"
            @click="typeBtn(e, value)"
            style="width: auto; padding: 11px 20px; cursor: pointer"
            :class="{ activeBtn: type == e.value }"
            :key="i"
          >
            {{ e.label }}
          </span>
        </div>
      </div>
      <div class="select_boot flex">
        <div>
          已选条件：
          <span v-for="(val, index) in providerSelect" :key="index">
            {{ val }}
            <img
              src="@/assets/images/solution/home/<USER>"
              alt=""
              v-if="providerSelect.length > 0"
              style="
                width: 16px;
                height: 16px;
                cursor: pointer;
                margin-right: 20px;
              "
              @click="deleteSelect(val, index)"
            />
          </span>
        </div>
        <div class="right_con">
          共找到
          <span>{{ totalItemCount }}</span> 条结果
        </div>
      </div>
    </div>

    <div class="tabContent">
      <div v-if="tableList && tableList.length > 0" style="width: 100%">
        <div class="AITips flex align-center" v-if="!showPagination">
          <img
            style="width: 40px; height: 40px; margin-right: 10px"
            src="@/assets/images/AI/ai.png"
            alt=""
          />
          <div class="words">以下是AI助手为您找到的相关结果</div>
        </div>
        <div class="cardContent">
          <div class="card_total flex-1">
            <template v-for="(item, index) in tableList" :key="index">
              <div
                :class="[
                  'card_content',
                  {
                    cardActive: cardActive == index,
                    rightActive: index % 2 != 0,
                    cardObvious: index < 2 && tableList.length < 3,
                    bottomLine:
                      (index == tableList.length - 1 ||
                        index == tableList.length - 2) &&
                      index > 1,
                  },
                ]"
                @mouseenter="contentColor(index)"
                @mouseleave="contentLeave"
                @click="proDetail(item)"
              >
                <div style="display: flex; margin: 24px">
                  <div>
                    <div class="imgTitle">
                      <div>{{ item.name }}</div>
                      <img v-if="appMarket == 2 && item.isKey == 1" class="mainScene" src="@/assets/images/scenario/mainSceneLeft.png" alt=""/>  
                    </div>
                  </div>
                  <div class="card_center">
                    <div class="card_text">
                      <div class="card_tag">
                        <a-tag color="#D7E6FF">{{ item.classifyName }}</a-tag>
                        <div class="card_title">{{ item.name }}</div>
                      </div>
                      <a-tag :bordered="false" class="cityStyle">{{
                        item.provider
                      }}</a-tag>
                    </div>
                    <div class="card_des">
                       {{ item.introduce }} 
                      <!--<div>目标客户：{{ item.customers }}</div>-->
                      <!--<div>需求方案组合：{{ item.list }}</div>-->
                    </div>
                    <div
                      class="flex"
                      style="justify-content: space-between"
                      v-if="item.typeName"
                    >
                      <div class="flex">
                        <a-tag
                          color="#D7E6FF"
                          style="
                            display: block;
                            color: rgba(0, 0, 0, 0.45);
                            background-color: transparent;
                            border: 1px solid #d9d9d9;
                            line-height: 17px;
                          "
                          >{{ item.typeName }}</a-tag
                        >
                      </div>
                    </div>
                    <div
                      style="
                        display: flex;
                        align-items: center;
                        justify-content: space-between;
                      "
                    >
                      <div
                        style="
                          display: flex;
                          align-items: center;
                          justify-content: space-between;
                          margin-right: 5px;
                        "
                      >
                        <div
                          style="
                            display: flex;
                            align-items: center;
                            margin-right: 18px;
                          "
                        >
                          <img
                            src="@/assets/images/home/<USER>"
                            style="width: 16px; height: 16px"
                            alt=""
                          />
                          <span
                            style="font-size: 12px; color: rgba(0, 0, 0, 0.45)"
                            v-if="item.viewCount"
                            >{{ item.viewCount }}</span
                          >
                          <span v-else>-</span>
                        </div>
                      </div>
                      <div v-if="appMarket == 2">
                        <button
                          class="cart-button"
                          disabled
                          v-if="item.addOrder"
                        >
                          <span class="add" style="color: rgba(0, 0, 0, 0.9)">
                            &nbsp;已加入</span
                          >
                        </button>
                        <button
                          class="cart-button pointer"
                          v-else
                          @click.stop="add(item.id)"
                        >
                          <img
                            v-if="!item.addCart"
                            class="add-icon"
                            src=" @/assets/images/AI/isadded.png"
                          /><span class="add"> &nbsp;加入定制</span>
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </template>
          </div>
        </div>
        <div class="layPage">
          <a-pagination
            v-model:pageSize="pageItemSize"
            v-model:current="currentPage"
            :pageSizeOptions="pageSizeOptions"
            show-quick-jumper
            show-size-changer
            :total="totalItemCount"
            @change="pageChange"
            @showSizeChange="sizeChange"
            class="mypage"
          />
        </div>
      </div>
      <div v-if="tableList.length == 0" class="emptyPhoto">
        <img src="@/assets/images/home/<USER>" alt=""/>
      </div>
      <div class="loading" v-show="loadingShow">
        <a-spin />
      </div>
    </div>
  </div>
</template>

<script>
import { defineComponent, reactive, toRefs, ref, watch } from "vue";
import { useRouter, useRoute } from "vue-router";
import { getSceneList, getLabelList } from "@/api/scenario/home";
import { addShop } from "@/api/buyList/index.js";
import voiceRecorder from "@/components/voiceRecorder/voiceRecorder.vue";
import zhCN from "ant-design-vue/es/locale/zh_CN";
import { getMakeUrl } from "@/utils/getUrl";
import eventBus from "@/utils/eventBus";
import { AISearch, AIvoice } from "@/api/AI/ai.js";
import loadingSmall from "@/components/superLoadingSmall/loadingSmall.vue";

export default defineComponent({
  components: {
    voiceRecorder,
    loadingSmall,
  },
  setup() {
    const baseURL = getMakeUrl();
    const vocation = ref("");
    const region = ref("");
    const box = ref(null);
    const selectData = ref(null);
    const Route = useRoute();
    const data = reactive({
      name: "",
      moment: "",
      loadingShow: true,
      classify: "",
      type: "",
      label: "",
      cardActive: "-1",
      pageSizeOptions: ["10", "20", "30", "50"],
      totalItemCount: 0,
      appMarket: 0,
      tabClassifyList: [],
      tabTypeList: [],
      tabLabelList: [],
      vocationList: [],
      tableList: [],
      totalNum: 0,
      totalItemCount1: 0,
      currentPage: 1,
      pageItemSize: 10,
      cityTicketList: [],
      labelIdlist: [],
      labelIdlist1: undefined,
      showLast: false,
      AIsearch: false,
      showId: undefined,
      selectList: [],
      selectListNew: [],
      showMore: false,
      showScense: "",
      morePro: true,
      providerSelect: [],
      showIndex: "",
      switchOnOff: true,
      showPagination: true,
      isTranslating: false,
      canBtnUse: false,
      tableAIAllList: [],
      filterArr:[],
    });
    function debounce(fn, delay) {
      let time = null;
      return function () {
        if (time) {
          clearTimeout(time);
        }
        time = setTimeout(() => {
          fn.call(this);
        }, 600);
      };
    }
    const getList = () => {
      data.loadingShow = false;
      let pageParams = {
        pageNo: data.currentPage,
        pageSize: data.pageItemSize,
        keyword: data.name, //关键字
        classify: data.classify, //分类
        type: data.type, //类型
        label: data.label, //标签
        applicationMarkets: Route.query.appMarket,
      };
      data.loadingShow = true;
      getSceneList(pageParams).then((res) => {
        data.showPagination = true;
        data.loadingShow = false;
        data.tableList = [];
        data.tableList = res.data.rows;
        data.totalItemCount = res.data.totalRows;
        data.tableList.forEach((item) => {
          item.list = item.demandSchemeList.map((item) => item.name).join(",");
        });
        if (data.classify == "") {
          data.totalItemCount1 = res.data.totalRows;
        }
      });
    };

    const getAIList = () => {
      if (data.name.trim() == "") {
        data.showPagination = true;
        getList();
        return false;
      }

      data.classify = '';
      data.type = '';
      data.label = "";
      data.showLast = false;
      data.showId = "";
      data.labelIdlist = [];
      data.providerSelect = [];
      data.selectList = [];
      data.selectListNew = [];
      
      data.loadingShow = true;
      data.AIsearch = true;
      AISearch({
        question: data.name,
        type: 5,
        resultType: Route.query.appMarket == 1 ? 4 : 7,
      }).then((res) => {
        data.loadingShow = false;
        data.AIsearch = false;
        if (res.code == 200) {
          data.showPagination = false;
          data.tableList = [];
          data.tableAIAllList = [];
          data.tableAIAllList = res.data;
          getAIPageList();
          data.totalItemCount = res.data ? res.data.length : 0;
          data.tableList.map((item) => {
            item.list = item.demandSchemeList
              .map((item) => item.name)
              .join(",");
          });
        }
      });
    };

    const refreshList = () => {
      if (!data.switchOnOff) {
        getList();
      } else {
        getAIList();
      }
    };
    eventBus.on("scenarioRefresh", refreshList);

    const getAIPageList = () => {
      let firstPageNum = (data.currentPage - 1) * data.pageItemSize;
      let lastPageNum = data.currentPage * data.pageItemSize;
      // if(data.label && data.label != ''){
      //   data.filterArr = data.tableAIAllList.filter(item=>{
      //     if(item.label){
      //       return val.value == item.label
      //     }
      //   })
      //   data.tableList = data.filterArr.slice(firstPageNum, lastPageNum);
      //   data.totalItemCount = data.filterArr.length
      // }else if(data.type && data.type != ''){
      //   data.filterArr = data.tableAIAllList.filter(item=>{
      //     if(item.type){
      //       return val.value == item.type
      //     }
      //   })
      //   data.tableList = data.filterArr.slice(firstPageNum, lastPageNum);
      //   data.totalItemCount = data.filterArr.length
      // }else if(data.classify && data.classify != ''){
      //   data.filterArr = data.tableAIAllList.filter(item=>{
      //     if(item.classify){
      //       return val.value == item.classify
      //     }
      //   })
      //   data.tableList = data.filterArr.slice(firstPageNum, lastPageNum);
      //   data.totalItemCount = data.filterArr.length
      // }else{
      // }
      data.tableList = data.tableAIAllList.slice(firstPageNum, lastPageNum);
    };
    const seekContent = debounce(function () {
      data.currentPage = 1;
      if (data.switchOnOff) {
        if(data.AIsearch){
          return
        }else{
          getAIList();
        }
      } else {
        getList();
      }
    },100);

    watch(
      () => Route.query.appMarket,
      (val) => {
        if (val) {
          data.appMarket = val;
          seekContent();
        }
      },
      { immediate: true }
    );

    const add = (id) => {
      addShop({
        productId: id,
        type: "1",
      }).then(() => {
        refreshList();
        eventBus.emit("cartRefresh");
      });
    };
    const regionChange = (val) => {
      region.value = val;
      getList();
    };
    const tabChange = (val) => {
      if (val.value !== data.classify) {
        data.classify = vocation.value = val.value;
        data.currentPage = 1;
        getList();
      }
    };
    const contentColor = (index) => {
      data.cardActive = index;
    };
    const router = useRouter();
    const proDetail = (val) => {
      router.push({
        query: {
          id: val.id,
          appMarket: Route.query.appMarket,
        },
        name: "scenarioDetail",
      });
    };
    const contentLeave = () => {
      data.cardActive = "-1";
    };
    const pageChange = (page) => {
      data.currentPage = page;
      if (data.showPagination) {
        getList();
      } else {
        getAIPageList();
      }
    };
    const sizeChange = (_current, size) => {
      data.pageItemSize = size;
      if (data.showPagination) {
        getList();
      } else {
        getAIPageList();
      }
    };
    const labelChange = (val) => {
      data.labelIdlist = val.join(",");
    };
    const getTarde = () => {
      getLabelList().then((result) => {
        data.tabClassifyList = result.data.rows.map((item) => ({
          label: item.name,
          value: item.id,
          length: item.children ? item.children.length : 0,
          children: item.children
            ? item.children.map((child) => ({
                label: child.name,
                value: child.id,
                children: undefined,
              }))
            : undefined,
        }));
      });
    };

    const providerEnter = (val) => {
      data.showId = val.value;
      data.showLast = true;
    };

    const providerLeave = () => {
      data.showId = "";
      data.showLast = false;
    };

    const labelSelect = (value, type = "default", index) => {
      if (value.children && type !== "last") {
        data.showLast = !data.showLast;
        data.showId = value.value;
        data.showScense = "label";
        data.showIndex = index;
      } else {
        if (data.selectList.includes(value.label)) {
          const index = data.selectList.findIndex(
            (item) => item === value.label
          );
          if (index !== -1) {
            data.selectList.splice(index, 1);
          }
          const index1 = data.selectListNew.findIndex(
            (item) => item === value.value
          );
          if (index1 !== -1) {
            data.selectListNew.splice(index, 1);
          }
        } else {
          data.selectList.push(value.label);
          data.selectListNew.push(value.value);
        }
        data.selectList = data.selectList.filter((value, index, self) => {
          return self.indexOf(value) === index;
        });
        data.selectListNew = data.selectListNew.filter((value, index, self) => {
          return self.indexOf(value) === index;
        });
        data.labelIdlist = data.selectListNew;
        data.labelIdlist = data.labelIdlist.join(",");
        getList();
      }
    };
    const providerBtn = (val) => {
      data.classify = vocation.value = val.value;
      let str = "场景分类：" + val.label;
      if (data.providerSelect.includes(str)) {
        data.providerSelect = [];
        data.classify = "";
      } else {
        data.providerSelect = [];
        data.providerSelect.push(str);
        data.providerSelect = data.providerSelect.filter(
          (value, index, self) => {
            return self.indexOf(value) === index;
          }
        );
      }
      data.currentPage = 1;
      getList();
    };
    const classifyBtn = (val) => {
      data.classify = vocation.value = val.value;
      data.type = "";
      data.label = "";
      let str = "场景分类：" + val.label;
      if (data.providerSelect.includes(str)) {
        data.providerSelect = [];
        data.classify = "";
      } else {
        data.providerSelect = [];
        data.providerSelect.push(str);
        data.providerSelect = data.providerSelect.filter(
          (value, index, self) => {
            return self.indexOf(value) === index;
          }
        );
      }
      data.currentPage = 1;
      if(data.switchOnOff){
        if(data.name != ''){
          data.filterArr = data.tableAIAllList.filter(item=>{
            if(item.classify){
              return val.value == item.classify
            }
          })
          let firstPageNum = (data.currentPage - 1) * data.pageItemSize;
          let lastPageNum = data.currentPage * data.pageItemSize;
          data.tableList = data.filterArr.slice(firstPageNum, lastPageNum);
          data.totalItemCount = data.filterArr.length
        }else{
          getList()
        }
      }else{
        getList();
      }
    };

    const typeBtn = (val, value) => {
      data.classify = value.value;
      data.type = val.value;
      data.label = "";
      let str1 = "场景分类：" + value.label;
      let str = "场景类型：" + val.label;
      if (data.providerSelect.includes(str)) {
        data.providerSelect = [str1];
        data.type = "";
      } else {
        data.providerSelect = [str1];
        data.providerSelect.push(str);
        data.providerSelect = data.providerSelect.filter(
          (value, index, self) => {
            return self.indexOf(value) === index;
          }
        );
      }
      data.currentPage = 1;
      if(data.switchOnOff){
        if(data.name != ''){
          data.filterArr = data.tableAIAllList.filter(item=>{
            if(item.type){
              return val.value == item.type
            }
          })
          let firstPageNum = (data.currentPage - 1) * data.pageItemSize;
          let lastPageNum = data.currentPage * data.pageItemSize;
          data.tableList = data.filterArr.slice(firstPageNum, lastPageNum);
          data.totalItemCount = data.filterArr.length
        }else{
          getList()
        }
      }else{
        getList();
      }
    };
    const labelBtn = (val) => {
      data.label = vocation.value = val.value;
      let str = "场景标签：" + val.label;
      if (data.providerSelect.includes(str)) {
        data.providerSelect = [data.providerSelect[0], data.providerSelect[1]];
        data.label = "";
      } else {
        data.providerSelect = [data.providerSelect[0], data.providerSelect[1]];
        data.providerSelect.push(str);
        data.providerSelect = data.providerSelect.filter(
          (value, index, self) => {
            return self.indexOf(value) === index;
          }
        );
      }
      data.currentPage = 1;
      if(data.switchOnOff){
        if(data.name != ''){
          data.filterArr = data.tableAIAllList.filter(item=>{
            if(item.label){
              return val.value == item.label
            }
          })
          let firstPageNum = (data.currentPage - 1) * data.pageItemSize;
          let lastPageNum = data.currentPage * data.pageItemSize;
          data.tableList = data.filterArr.slice(firstPageNum, lastPageNum);
          data.totalItemCount = data.filterArr.length
        }else{
          getList()
        }
      }else{
        getList();
      }
    };

    const deleteSelect = (val, index) => {
      if (index == 0) {
        data.providerSelect = [];
        data.classify = "";
        data.type = "";
        data.label = "";
      }
      if (index == 1) {
        data.providerSelect = [data.providerSelect[0]];
        data.type = "";
        data.label = "";
      }
      if (index == 2) {
        data.providerSelect = [data.providerSelect[0], data.providerSelect[1]];
        data.label = "";
      }
      if(data.switchOnOff){
        if(data.name != ''){
          data.filterArr = data.tableAIAllList.filter(item=>{
            if(item.classify){
              return val.value == item.classify
            }
          })
          let firstPageNum = (data.currentPage - 1) * data.pageItemSize;
          let lastPageNum = data.currentPage * data.pageItemSize;
          data.tableList = data.filterArr.slice(firstPageNum, lastPageNum);
          data.totalItemCount = data.filterArr.length
        }else{
          getList()
        }
      }else{
        getList();
      }
    };
    const showMore = (type, index) => {
      if (type == "provider") {
        data.showScense = type;
        data.morePro = false;
      } else {
        data.showIndex = index;
        data.showScense = type;
      }
    };
    const showless = (type, index) => {
      if (type == "provider_less") {
        data.showScense = type;
        data.morePro = true;
      } else {
        data.showIndex = "";
        data.showScense = type;
      }
    };

    const getBoxTitle = (key) => {
      const rect = box.value[key].getBoundingClientRect();
      const selectHtml = selectData.value.getBoundingClientRect();
      return {
        left: rect.x - selectHtml.x + "px",
        top: rect.y - selectHtml.y + "px",
      };
    };

    const getBoxLeft = (key) => {
      const rect = box.value[key].getBoundingClientRect();
      const firstRect = box.value[0].getBoundingClientRect();
      const selectHtml = selectData.value.getBoundingClientRect();
      return {
        left: firstRect.x - selectHtml.x + "px",
        top: rect.y - selectHtml.y + 49 + "px",
      };
    };

    getTarde();
    // 语音输入
    const handleAudio = (audioBlob) => {
      const formData = new FormData();
      formData.append("file", audioBlob, "recording.wav"); // 上传文件
      // 调用 AIvoice 函数并传递音频数据
      data.isTranslating = true;
      data.canBtnUse = true;
      AIvoice(formData).then((res) => {
        data.isTranslating = false;
        data.canBtnUse = false;
        if (res.code == 200) {
          data.name = res.msg;
          // seekContent()
        }
      });
    };
    return {
      ...toRefs(data),
      debounce,
      box,
      selectData,
      vocation,
      providerEnter,
      providerLeave,
      labelSelect,
      providerBtn,
      classifyBtn,
      typeBtn,
      labelBtn,
      showMore,
      showless,
      deleteSelect,
      region,
      regionChange,
      tabChange,
      contentColor,
      sizeChange,
      contentLeave,
      add,
      proDetail,
      router,
      pageChange,
      zhCN,
      seekContent,
      baseURL,
      labelChange,
      getAIList,
      refreshList,
      handleAudio,
      getBoxTitle,
      getBoxLeft,
    };
  },
});
</script>

<style lang="scss" scoped src="./userScene.scss"></style>

<style lang="scss">
.ant-select:not(.ant-select-customize-input) .ant-select-selector {
  border: none;
}

::v-deep(.ant-cascader-input.ant-input) {
  border: none !important;
}

.mypage {
  .ant-pagination {
    color: rgba(0, 0, 0, 0.65);
  }

  .ant-select-selector {
    color: rgba(0, 0, 0, 0.65);
  }

  .ant-pagination-item-active {
    background: #007eff;
  }

  .ant-pagination-item-active a {
    color: #ffffff;
  }

  .ant-pagination-item-active:focus a,
  .ant-pagination-item-active:hover a {
    color: #ffffff;
  }

  .ant-pagination-item-active a:focus,
  .ant-pagination-item-active a:hover {
    color: #ffffff;
  }

  .ant-pagination-item:focus,
  .ant-pagination-item:hover {
    background: #007eff;
  }

  .ant-pagination-item:focus a,
  .ant-pagination-item:hover a {
    color: #ffffff;
  }

  .ant-pagination-item-disabled:hover,
  .ant-pagination-item-disabled:focus {
    background-color: #ffffff;
    border-color: #d9d9d9;

    a {
      color: rgba(0, 0, 0, 0.25);
    }
  }

  .ant-pagination-options-size-changer.ant-select {
    margin-right: 0;
  }

  .ant-pagination-options-quick-jumper {
    margin-left: 8px;
  }
}
</style>
