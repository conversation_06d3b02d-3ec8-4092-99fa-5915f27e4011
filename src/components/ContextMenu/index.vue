<template>
  <div v-if="visible" 
       class="context-menu" 
       :style="{ left: x + 'px', top: y + 'px' }"
       @click="handleMenuClick">
    <div v-for="(item, index) in menuItems" 
         :key="index"
         class="context-menu-item" 
         @click.stop="handleItemClick(item, index)">
      <span class="menu-text">{{ item.label }}</span>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue';

// 定义组件属性
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  x: {
    type: Number,
    default: 0
  },
  y: {
    type: Number,
    default: 0
  },
  menuItems: {
    type: Array,
    default: () => []
  }
});

// 定义事件
const emit = defineEmits(['item-click', 'menu-click']);

// 处理菜单项点击
const handleItemClick = (item, index) => {
  emit('item-click', { item, index });
};

// 处理菜单点击（用于隐藏菜单）
const handleMenuClick = () => {
  emit('menu-click');
};

// 点击外部隐藏菜单
const handleClickOutside = (event) => {
  if (props.visible) {
    emit('menu-click');
  }
};

// 生命周期管理
onMounted(() => {
  document.addEventListener('click', handleClickOutside);
});

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside);
});
</script>

<style scoped>
.context-menu {
  position: fixed;
  background: #ffffff;
  border: 1px solid #e5e6eb;
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  min-width: 120px;
  padding: 4px 0;
}

.context-menu-item {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  cursor: pointer;
  font-size: 14px;
  color: #333333;
  transition: background-color 0.2s;
}

.context-menu-item:hover {
  background-color: #f5f5f5;
}

.menu-icon {
  width: 16px;
  height: 16px;
  margin-right: 8px;
}

.menu-text {
  flex: 1;
}
</style> 