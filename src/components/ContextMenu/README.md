# ContextMenu 右键菜单组件

## 概述

`ContextMenu` 是一个可复用的右键菜单组件，支持自定义菜单项、图标和事件处理。

## 功能特性

- ✅ 支持自定义菜单项
- ✅ 支持图标显示
- ✅ 自动定位到鼠标位置
- ✅ 点击外部自动隐藏
- ✅ 支持键盘事件
- ✅ 完全可定制样式
- ✅ TypeScript 支持

## 基础用法

### 1. 引入组件和组合式函数

```vue
<template>
  <div>
    <!-- 你的内容 -->
    <div @contextmenu.prevent="showContextMenu($event, item)">
      右键点击我
    </div>
    
    <!-- 右键菜单组件 -->
    <ContextMenu
      :visible="contextMenuVisible"
      :x="contextMenuX"
      :y="contextMenuY"
      :menuItems="menuItems"
      @item-click="handleMenuItemClick"
      @menu-click="handleMenuClick"
    />
  </div>
</template>

<script setup>
import ContextMenu from '@/components/ContextMenu/index.vue';
import { useContextMenu, createDownloadMenuItem, createPreviewMenuItem } from '@/composables/useContextMenu.js';

// 使用组合式函数
const { 
  contextMenuVisible, 
  contextMenuX, 
  contextMenuY, 
  showContextMenu,
  handleMenuItemClick,
  handleMenuClick
} = useContextMenu([
  createDownloadMenuItem('下载文件', '/path/to/download-icon.png'),
  createPreviewMenuItem('预览文件', '/path/to/preview-icon.png')
]);

// 处理菜单项点击
const handleMenuAction = (selectedItem, menuItem, index) => {
  console.log('选中的项目:', selectedItem);
  console.log('菜单项:', menuItem);
  console.log('索引:', index);
  
  // 根据菜单项执行不同操作
  switch (menuItem.action) {
    case 'download':
      downloadFile(selectedItem);
      break;
    case 'preview':
      previewFile(selectedItem);
      break;
  }
};
</script>
```

### 2. 自定义菜单项

```javascript
import { createMenuItem } from '@/composables/useContextMenu.js';

const customMenuItems = [
  createMenuItem('编辑', '/path/to/edit-icon.png', 'edit'),
  createMenuItem('删除', '/path/to/delete-icon.png', 'delete'),
  createMenuItem('复制', '/path/to/copy-icon.png', 'copy')
];

const { 
  contextMenuVisible, 
  contextMenuX, 
  contextMenuY, 
  showContextMenu,
  handleMenuItemClick,
  handleMenuClick
} = useContextMenu(customMenuItems);
```

### 3. 动态菜单项

```javascript
// 根据选中项目动态生成菜单项
const showDynamicMenu = (event, item) => {
  const menuItems = [];
  
  if (item.canEdit) {
    menuItems.push(createMenuItem('编辑', '/path/to/edit-icon.png', 'edit'));
  }
  
  if (item.canDelete) {
    menuItems.push(createMenuItem('删除', '/path/to/delete-icon.png', 'delete'));
  }
  
  showContextMenu(event, item, menuItems);
};
```

## API 文档

### ContextMenu 组件 Props

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| visible | Boolean | false | 是否显示菜单 |
| x | Number | 0 | 菜单 X 坐标 |
| y | Number | 0 | 菜单 Y 坐标 |
| menuItems | Array | [] | 菜单项配置 |

### ContextMenu 组件 Events

| 事件名 | 参数 | 说明 |
|--------|------|------|
| item-click | { item, index } | 菜单项点击事件 |
| menu-click | - | 菜单点击事件（用于隐藏菜单） |

### 菜单项配置

```javascript
{
  label: '菜单项文字',
  icon: '/path/to/icon.png', // 可选
  action: 'action-name' // 可选，用于标识动作类型
}
```

### useContextMenu 返回值

| 属性/方法 | 类型 | 说明 |
|-----------|------|------|
| contextMenuVisible | Ref<Boolean> | 菜单显示状态 |
| contextMenuX | Ref<Number> | 菜单 X 坐标 |
| contextMenuY | Ref<Number> | 菜单 Y 坐标 |
| selectedItem | Ref<Object> | 选中的项目 |
| menuItems | Ref<Array> | 菜单项列表 |
| showContextMenu | Function | 显示菜单方法 |
| hideContextMenu | Function | 隐藏菜单方法 |
| handleMenuItemClick | Function | 处理菜单项点击 |
| handleMenuClick | Function | 处理菜单点击 |

## 使用示例

### 文件列表右键菜单

```vue
<template>
  <div class="file-list">
    <div 
      v-for="file in files" 
      :key="file.id"
      class="file-item"
      @contextmenu.prevent="showContextMenu($event, file)"
    >
      {{ file.name }}
    </div>
    
    <ContextMenu
      :visible="contextMenuVisible"
      :x="contextMenuX"
      :y="contextMenuY"
      :menuItems="fileMenuItems"
      @item-click="handleFileAction"
      @menu-click="handleMenuClick"
    />
  </div>
</template>

<script setup>
import ContextMenu from '@/components/ContextMenu/index.vue';
import { useContextMenu, createDownloadMenuItem, createPreviewMenuItem } from '@/composables/useContextMenu.js';

const downloadIcon = new URL('@/assets/images/download.png', import.meta.url).href;
const previewIcon = new URL('@/assets/images/preview.png', import.meta.url).href;

const { 
  contextMenuVisible, 
  contextMenuX, 
  contextMenuY, 
  showContextMenu,
  handleMenuItemClick,
  handleMenuClick
} = useContextMenu([
  createDownloadMenuItem('下载文件', downloadIcon),
  createPreviewMenuItem('预览文件', previewIcon)
]);

const handleFileAction = (selectedFile, menuItem, index) => {
  switch (menuItem.action) {
    case 'download':
      downloadFile(selectedFile);
      break;
    case 'preview':
      previewFile(selectedFile);
      break;
  }
};
</script>
```

### 表格行右键菜单

```vue
<template>
  <table>
    <tr 
      v-for="row in tableData" 
      :key="row.id"
      @contextmenu.prevent="showContextMenu($event, row)"
    >
      <td>{{ row.name }}</td>
      <td>{{ row.status }}</td>
    </tr>
  </table>
  
  <ContextMenu
    :visible="contextMenuVisible"
    :x="contextMenuX"
    :y="contextMenuY"
    :menuItems="tableMenuItems"
    @item-click="handleTableAction"
    @menu-click="handleMenuClick"
  />
</template>

<script setup>
import ContextMenu from '@/components/ContextMenu/index.vue';
import { useContextMenu, createMenuItem } from '@/composables/useContextMenu.js';

const { 
  contextMenuVisible, 
  contextMenuX, 
  contextMenuY, 
  showContextMenu,
  handleMenuItemClick,
  handleMenuClick
} = useContextMenu([
  createMenuItem('编辑', '/path/to/edit-icon.png', 'edit'),
  createMenuItem('删除', '/path/to/delete-icon.png', 'delete'),
  createMenuItem('复制', '/path/to/copy-icon.png', 'copy')
]);

const handleTableAction = (selectedRow, menuItem, index) => {
  switch (menuItem.action) {
    case 'edit':
      editRow(selectedRow);
      break;
    case 'delete':
      deleteRow(selectedRow);
      break;
    case 'copy':
      copyRow(selectedRow);
      break;
  }
};
</script>
```

## 注意事项

1. **事件阻止默认行为**：在触发右键菜单的元素上使用 `@contextmenu.prevent`
2. **菜单定位**：组件会自动根据鼠标位置定位菜单
3. **自动隐藏**：点击菜单外部会自动隐藏菜单
4. **内存管理**：组件会自动管理事件监听器，无需手动清理
5. **样式定制**：可以通过 CSS 变量或覆盖样式来自定义外观

## 样式定制

```scss
// 自定义菜单样式
.context-menu {
  --menu-bg-color: #ffffff;
  --menu-border-color: #e5e6eb;
  --menu-text-color: #333333;
  --menu-hover-bg: #f5f5f5;
  
  background: var(--menu-bg-color);
  border: 1px solid var(--menu-border-color);
  border-radius: 8px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}
``` 