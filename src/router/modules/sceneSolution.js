import BasicLayout from '@/layout/index.vue'

let ROUTES = [
    {
        path: '/sceneSolution',
        name: 'sceneSolution',
        component: BasicLayout,
        hidden: true,
        meta: {
            title: "场景方案",
            KeepAlive: true,
        },
        children: [
            {
                path: 'topContentScene',
                name: 'topContentScene',
                meta: {
                    KeepAlive: true,
                },
                component: () => import('@/views/sceneSolution/homePage/topContent/index.vue')
            },
            {
                path: 'applyNew',
                name: 'applyNew',
                component: () => import('@/views/sceneSolution/detail/applyDetail.vue')

            },
        ]
    },
    {
        path: '/viewFile',
        component: () => import('@/views/solution/viewFile/index.vue'),
        name: 'viewFile'
    }
]

export default ROUTES;