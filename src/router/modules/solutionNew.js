import BasicLayout from '@/layout/index.vue'

let ROUTES = [
    {
        path: '/solveNew',
        name: 'solveNew',
        component: BasicLayout,
        hidden: true,
        meta: {
            title: "能力",
            KeepAlive: true,
        },
        children: [
            {
                path: 'topContentNew',
                name: 'topContentNew',
                meta: {
                	KeepAlive: true,
                },
                component: () => import('@/views/solutionNew/homePage/topContent/index.vue')
            },
            {
                path: 'detailNew',
                name: 'solveDetailNew',
                component: () => import('@/views/solutionNew/detail/index.vue')

            },
//          {
//              path: 'applyNew',
//              name: 'applyNew',
//              component: () => import('@/views/solutionNew/detail/applyDetail.vue')
//
//          },
        ]
    },
    {
        path: '/viewFile',
        component: () => import('@/views/solution/viewFile/index.vue'),
        name: 'viewFile'
    }
]

export default ROUTES;