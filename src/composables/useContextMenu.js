import { ref } from 'vue';

/**
 * 右键菜单组合式函数
 * @param {Array} defaultMenuItems - 默认菜单项配置
 * @returns {Object} 右键菜单相关的状态和方法
 */
export function useContextMenu(defaultMenuItems = []) {
  // 菜单状态
  const contextMenuVisible = ref(false);
  const contextMenuX = ref(0);
  const contextMenuY = ref(0);
  const selectedItem = ref(null);
  const menuItems = ref(defaultMenuItems);

  /**
   * 显示右键菜单
   * @param {Event} event - 鼠标事件
   * @param {Object} item - 选中的项目数据
   * @param {Array} customMenuItems - 自定义菜单项（可选）
   */
  const showContextMenu = (event, item, customMenuItems = null) => {
    event.preventDefault();
    contextMenuVisible.value = true;
    contextMenuX.value = event.clientX;
    contextMenuY.value = event.clientY;
    selectedItem.value = item;
    
    // 如果提供了自定义菜单项，则使用自定义的
    if (customMenuItems) {
      menuItems.value = customMenuItems;
    }
  };

  /**
   * 隐藏右键菜单
   */
  const hideContextMenu = () => {
    contextMenuVisible.value = false;
    selectedItem.value = null;
  };

  /**
   * 处理菜单项点击
   * @param {Function} callback - 回调函数，接收选中的项目和菜单项信息
   */
  const handleMenuItemClick = (callback) => {
    return ({ item, index }) => {
      if (callback && typeof callback === 'function') {
        callback(selectedItem.value, item, index);
      }
      // 不在这里自动隐藏菜单，让调用方决定何时隐藏
    };
  };

  /**
   * 处理菜单点击（隐藏菜单）
   */
  const handleMenuClick = () => {
    hideContextMenu();
  };

  return {
    // 状态
    contextMenuVisible,
    contextMenuX,
    contextMenuY,
    selectedItem,
    menuItems,
    
    // 方法
    showContextMenu,
    hideContextMenu,
    handleMenuItemClick,
    handleMenuClick
  };
}

/**
 * 创建下载菜单项
 * @param {string} label - 菜单项文字
 * @param {string} icon - 图标路径
 * @returns {Object} 菜单项配置
 */
export function createDownloadMenuItem(label = '下载附件', icon = null) {
  return {
    label,
    icon,
    action: 'download'
  };
}

/**
 * 创建预览菜单项
 * @param {string} label - 菜单项文字
 * @param {string} icon - 图标路径
 * @returns {Object} 菜单项配置
 */
export function createPreviewMenuItem(label = '预览文件', icon = null) {
  return {
    label,
    icon,
    action: 'preview'
  };
}

/**
 * 创建自定义菜单项
 * @param {string} label - 菜单项文字
 * @param {string} icon - 图标路径
 * @param {string} action - 动作标识
 * @returns {Object} 菜单项配置
 */
export function createMenuItem(label, icon = null, action = '') {
  return {
    label,
    icon,
    action
  };
} 