// import { downloadFile } from "@/api/file";

import PopMsg from "@/components/popMsg/index.vue";
import { createVNode } from "vue";
import { message } from "ant-design-vue";
import dayjs from "dayjs";

/******************************************************************
 * 函数名: getLocalStorage
 * 功能: 获取本地存储数据
 * 参数: key(String)指定的键
 * 返回值: 转换后的对象
 * Author: LF
 ******************************************************************/

export function getLocalStorage(key, type = 0) {
  if (type == 0) {
    return sessionStorage.getItem(key);
  }
  if (type == 1) {
    return JSON.parse(sessionStorage.getItem(key));
  }
}

/******************************************************************
 * 函数名: setLocalStorage
 * 功能: 设置本地存储数据
 * 参数: key(String)键，
 *  value(Object|String)值
 * 返回值: 无
 * Author: LF
 ******************************************************************/

export function setLocalStorage(key, value, type = 0) {
  if (type == 0) {
    sessionStorage.setItem(key, String(value));
    return;
  }

  if (type == 1) {
    sessionStorage.setItem(key, JSON.stringify(value));
  }
}

/*****************************************************************
 * 函数名: removeLocalStorage
 * 功能: 清除本地存储数据
 * 参数: key(String)键
 * 返回值: 无
 * Author: LF
 ******************************************************************/

export function removeLocalStorage(key) {
  window.sessionStorage.removeItem(key);
}

/*****************************************************************
 * 函数名: removeAllLocalStorage
 * 功能: 清除所有本地存储数据
 * 参数: 无
 * 返回值: 无
 * Author: ZH
 ******************************************************************/
export function removeAllLocalStorage() {
  for (let key in window.sessionStorage) {
    window.sessionStorage.removeItem(key);
  }
}

/*****************************************************************
 * 函数名: padURL
 * 功能: 补全地址
 * 参数: url(String)后台地址
 * 返回值: url
 * Author: LF
 ******************************************************************/
export function padURL(url) {
  let origin =
    document.location.origin === "https://xztsbank.com.cn"
      ? document.location.origin
      : "http://159.156.1.51:12081";
  return origin + "/img/" + url;
}

/*****************************************************************
 * 函数名: downFile
 * 功能: 下载文件
 * 参数: ret 后台返回的值
 *      fileName 文件名称
 * 返回值: url
 * Author: LF
 ******************************************************************/
export function downFile(ret, fileName) {
  let windowOrigin = window.location.origin;
  let token = localStorage.getItem("token");
  let newHref = ret;
  if (ret.includes(windowOrigin)) {
    newHref = "/portal" + ret.split(windowOrigin)[1]
  }
  window.open(windowOrigin + newHref + "?token=" + token);
  return false;
  let blob = new Blob([ret], {
    type: "application/vnd.ms-excel",
  });
  let elink = document.createElement("a");
  elink.download = fileName;
  elink.style.display = "none";
  elink.href = URL.createObjectURL(blob);
  document.body.appendChild(elink);
  elink.click();
  URL.revokeObjectURL(elink.href); // 释放URL 对象
  document.body.removeChild(elink);
}

// 深拷贝
export function deepCopy(data) {
  if (typeof data !== "object" || data === null) {
    return data;
  }
  let newData = {};
  if (data instanceof Array) {
    newData = [];
  }

  const dataKeys = Object.keys(data);
  dataKeys.forEach((value) => {
    const currentDataValue = data[value];
    // 基本数据类型的值和函数直接赋值拷贝
    if (typeof currentDataValue !== "object" || currentDataValue === null) {
      newData[value] = currentDataValue;
    } else if (Array.isArray(currentDataValue)) {
      // 实现数组的深拷贝
      newData[value] = [...currentDataValue];
    } else if (currentDataValue instanceof Set) {
      // 实现set数据的深拷贝
      newData[value] = new Set([...currentDataValue]);
    } else if (currentDataValue instanceof Map) {
      // 实现map数据的深拷贝
      newData[value] = new Map([...currentDataValue]);
    } else {
      // 普通对象则递归赋值
      newData[value] = deepCopy(currentDataValue);
    }
  });
  return newData;
}

// export function download(fileNum) {
//   let blobUrl = downloadFile(fileNum);
//   let link = document.createElement("a");
//   document.body.appendChild(link);
//   link.style.display = "none";
//   link.href = blobUrl; // 设置a标签的下载属性，设置文件名及格式，后缀名最好让后端在数据格式中返回
//   link.download = fileNum; // 自触发click事件
//   link.click();
//   document.body.removeChild(link);
//   window.URL.revokeObjectURL(blobUrl);
// }

// 辅助函数：递归获取最后一级children，用于获得最后一个没有children或children长度为0的菜单对象
export const getLastChildren = (obj) => {
  if (obj.children && obj.children.length) {
    getLastChildren(obj.children);
  }
  return obj;
};

export const getFirstNode = (obj) => {
  if (obj.children && obj.children.length) {
    return getFirstNode(obj.children[0]);
  }
  return obj;
};

export function popMsg(msg, sucOrErr = "success") {
  message.destroy();
  const multilineMsg = createVNode(PopMsg, { message: msg });

  if (sucOrErr === "success") {
    message.success({
      content: multilineMsg,
      onClick: function () {
        message.destroy();
      },
    });
  } else {
    message.warning({
      content: multilineMsg,
      onClick: function () {
        message.destroy();
      },
    });
  }
}

export function loopObjAndAssign(obj, target) {
  for (const [key, value] of Object.entries(obj)) {
    target[key] = deepCopy(value);
  }
}

export function extractNamesFromMenuJson(json) {
  let resultArr = [];

  function extractNames(obj) {
    if (obj.hasOwnProperty("name")) {
      resultArr.push(obj.name);
    }

    if (obj.hasOwnProperty("children") && Array.isArray(obj.children)) {
      obj.children.forEach((child) => {
        extractNames(child);
      });
    }
  }

  let tabKeys = Object.keys(json);
  tabKeys.forEach((key) => {
    extractNames(json[key]);
  });
  return resultArr;
}

export function getExpendMenuName(menuList, selectedKeys) {
  let nameResult = [];

  let key = 0;
  let res;
  for (let i = 0; i < menuList.length; i++) {
    if (menuList[i].children) {
      res = menuList[i].children;
      for (let j = 0; j < res.length; j++) {
        if (res[j].name === selectedKeys) {
          key = i;
        }
      }
    }
  }

  if (menuList[key] !== undefined) {
    nameResult[0] = menuList[key].name;
  }
  return nameResult;
}

// 获取区间内任意值
export function getRandomArbitrary(min, max) {
  return Math.random() * (max - min) + min;
}

// 根据提供参数，自动生成假数据
export function getRandomNumberArr(basicLine, floatRange, total) {
  if (floatRange <= 0) {
    throw new Error("floatRange must be a positive value.");
  }
  const result = [];
  for (let i = 0; i < total; i++) {
    const randomNumber =
      basicLine + getRandomArbitrary(-floatRange, floatRange);
    result.push(Math.round(randomNumber));
  }

  return result;
}

/******************************************************************
 * 函数名: getLocalStorage
 * 功能: 获取本地存储数据
 * 参数: key(String)指定的键
 * 返回值: 转换后的对象
 * Author: LF
 ******************************************************************/

export function getSessionStorage(key, type = 0) {
  if (type == 0) {
    return localStorage.getItem(key);
  }
  if (type == 1) {
    return JSON.parse(localStorage.getItem(key));
  }
}

/******************************************************************
 * 函数名: setLocalStorage
 * 功能: 设置本地存储数据
 * 参数: key(String)键，
 *  value(Object|String)值
 * 返回值: 无
 * Author: LF
 ******************************************************************/

export function setSessionStorage(key, value, type = 0) {
  if (type == 0) {
    localStorage.setItem(key, String(value));
    return;
  }

  if (type == 1) {
    localStorage.setItem(key, JSON.stringify(value));
  }
}

// allowRolesList内用户有权限查看
export const isManager = (roles, allowRolesList) => {
  const allowRoles = allowRolesList;
  return roles.some((item) => allowRoles.includes(item));
};


//tootip判断是否显示
export const isShowToolTip = (item, num) => {
  return item && item.length >= num
};

export function getDateTime(type) {
  var date = new Date();
  var hengGang = "-";
  var maoHao = ":";
  var year = date.getFullYear();
  var month = date.getMonth() + 1;
  var curDate = date.getDate();
  var curHours = date.getHours();
  var curMinutes = date.getMinutes();
  var curSeconds = date.getSeconds();

  if (month >= 1 && month <= 9) {
    month = "0" + month;
  }
  if (curDate >= 0 && curDate <= 9) {
    curDate = "0" + curDate;
  }
  if (curHours >= 0 && curHours <= 9) {
    curHours = "0" + curHours;
  }
  if (curMinutes >= 0 && curMinutes <= 9) {
    curMinutes = "0" + curMinutes;
  }
  if (curSeconds >= 0 && curSeconds <= 9) {
    curSeconds = "0" + curSeconds;
  }

  var currentdate = "";
  if (type == "year") {
    currentdate = year;
    return currentdate;
  } else if (type == "month") {
    currentdate = year + hengGang + month;
    return currentdate;
  } else if (type == "date") {
    currentdate = year + hengGang + month + hengGang + curDate;
    return currentdate;
  } else if (type == "hour") {
    currentdate = year + hengGang + month + hengGang + curDate + " " + curHours + ":00:00";
    return currentdate;
  } else if (type == "minute") {
    currentdate = year + hengGang + month + hengGang + curDate + " " + curHours + maoHao + curMinutes + ":00";
    return currentdate;
  } else if (type == "second") {
    currentdate = year + hengGang + month + hengGang + curDate + " " + curHours + maoHao + curMinutes + maoHao + curSeconds;
    return currentdate;
  }
}

// 获取树组件对应id数组
export function getTargetId(nodes, targetId, path = []) {
  for (let node of nodes) {
    if (node.id === targetId) {
      return [...path, node.id];
    }
    if (node.children) {
      const result = getTargetId(node.children, targetId, [...path, node.id]);
      if (result) {
        return result;
      }
    }
  }
  return null;
}

export const valueValid = (rule, value) => {
  return new Promise((resolve, reject) => {
    let clearValue = value ? value.replace(/\s/g, "") : "";
    if (value && (clearValue.length == 0)) {
      reject("输入框值不可输入空字符串");
    } else {
      resolve();
    }
  });
};

// 数字转汉字
export const toChinese = (num) => {
  const chineseDigits = ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九'];
  const chineseUnits = ['', '十', '百', '千', '万', '十万', '百万', '千万', '亿'];

  if (num === 0) {
    return '零';
  }

  let result = '';
  let unitPos = 0;
  let zeroFlag = false;

  while (num > 0) {
    const digit = num % 10;

    if (digit === 0) {
      if (!zeroFlag) {
        result = chineseDigits[digit] + result;
        zeroFlag = true;
      }
    } else {
      result = chineseDigits[digit] + chineseUnits[unitPos] + result;
      zeroFlag = false;
    }

    unitPos++;
    num = Math.floor(num / 10);
  }

  // 处理数字十的简写规则
  result = result.replace(/^一十/, '十');

  return result;
}

/**
 * 格式化日期时间为 'YYYY-MM-DD HH:mm:ss'，无效或空返回'-'
 * @param {string|Date|any} time
 * @returns {string}
 */
export function formatDateTime(time) {
  if (!time || time === '-') return "-";
  return dayjs(time).isValid() ? dayjs(time).format("YYYY-MM-DD HH:mm:ss") : "-";
}
/**
 * 将数据按照年月进行归组
 * @param {Array} data - 包含时间字段的数据数组
 * @param {string} timeField - 时间字段名，默认为 'createTime'
 * @returns {Object} 按 'YYYY-MM' 格式归组的数据对象
 */
function groupDataByMonth(data, timeField = 'createTime') {
  const grouped = {};

  // 首先按时间排序数据
  const sortedData = [...data].sort((a, b) => {
    return new Date(b[timeField]) - new Date(a[timeField]);
  });

  sortedData.forEach(item => {
    // 获取时间字段值
    const timeValue = item[timeField];
    if (!timeValue) return;

    // 解析时间字符串，提取年月
    const date = new Date(timeValue);
    const yearMonth = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;

    // 如果该年月还没有数组，则创建一个
    if (!grouped[yearMonth]) {
      grouped[yearMonth] = [];
    }

    // 将当前项添加到对应的年月分组中
    grouped[yearMonth].push(item);
  });

  return grouped;
}

/**
 * 将归组结果转换为更友好的格式，包含格式化时间
 * @param {Array} data - 原始数据
 * @returns {Array} 包含年月信息和数据的数组
 */
export function groupDataByMonthWithStats(data) {
  const grouped = groupDataByMonth(data, 'createTime');

  return Object.entries(grouped)
    .map(([yearMonth, items]) => ({
      yearMonth,
      items: items.map(item => {
        const date = new Date(item.createTime);
        const day = date.getDate();
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');
        const seconds = String(date.getSeconds()).padStart(2, '0');

        return {
          ...item,
          edit: false,
          formattedTime: `${day}日 ${hours}:${minutes}`
        };
      })
    }))
    .sort((a, b) => a.yearMonth.localeCompare(b.yearMonth)); // 按时间排序
}
// 排序函数：同意的排第一，否则按时间最新拒绝的排序
export function sortTextList(textList) {
  if (!Array.isArray(textList) || textList.length === 0) {
    return textList;
  }
  return textList.sort((a, b) => {
    const statusA = a.dealType;
    const statusB = b.dealType;
    const timeA = new Date(a.dealTime);
    const timeB = new Date(b.dealTime);
    // 判断是否为同意状态（可根据实际业务逻辑调整）
    const isAgreeA = statusA === '1';
    const isAgreeB = statusB === '1';
    // 如果A是同意，B不是同意，A排在前面
    if (isAgreeA && !isAgreeB) {
      return -1;
    }
    // 如果B是同意，A不是同意，B排在前面
    if (!isAgreeA && isAgreeB) {
      return 1;
    }
    // 如果都是同意或都不是同意，按时间倒序排列（最新的在前）
    return timeB - timeA;
  });
}
// 评分页面去重处理：同一公司多条反馈时，优先保留非拒绝项
export function processDuplicateCompaniesByName(editFinishDataArg = []) {
  // 1. 参数验证
  if (!Array.isArray(editFinishDataArg)) {
    return [];
  }
  // 2. 使用Map按公司名称分组
  const groupedCompanies = new Map();
  const invalidItems = [];
  editFinishDataArg.forEach((item) => {
    if (!item || item.company == null) {
      invalidItems.push(item);
      return; // 跳过无效条目或没有名称的条目
    }
    const companyName = item.company.trim(); // 去除前后空格
    if (!groupedCompanies.has(companyName)) {
      groupedCompanies.set(companyName, []);
    }
    groupedCompanies.get(companyName).push(item);
  });
  // 3. 处理每组数据
  const result = [...invalidItems]; // 将无效项添加到结果中
  groupedCompanies.forEach((items, companyName) => {
    if (items.length === 1) {
      result.push(items[0]); // 唯一记录直接保留
      return;
    }
    // 4. 处理重复公司名称的记录
    const nonRejectedItems = items.filter(
      (item) => item.dealContent !== "拒绝"
    );
    if (nonRejectedItems.length > 0) {
      // 如果有非拒绝记录，保留这些
      result.push(...nonRejectedItems);
    } else {
      // 如果全部都是拒绝的，保留第一条
      result.push(items[0]);
    }
  });
  return result;
}
export function isSamePerson(person1, person2) {
  if (!person1 || !person2) return false;
  // 优先使用 userId 判断
  if (person1.userId && person2.userId) {
    return person1.userId === person2.userId;
  }
  // 其次使用 contactPhone 判断
  if (person1.contactPhone && person2.contactPhone) {
    return person1.contactPhone === person2.contactPhone;
  }
  // 最后使用 contactName 判断（需要同时匹配姓名和电话）
  if (person1.contactName && person2.contactName &&
    person1.contactPhone && person2.contactPhone) {
    return person1.contactName === person2.contactName &&
      person1.contactPhone === person2.contactPhone;
  }
  return false;
};