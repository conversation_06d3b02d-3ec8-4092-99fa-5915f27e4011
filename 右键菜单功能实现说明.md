# 右键菜单功能实现说明

## 功能概述
在 `src/views/solutionNew/detail/index.vue` 文件中，为方案附件列表区域添加了鼠标右键菜单功能，用户可以通过右键点击附件来下载文件。

**已封装为可复用组件**：`src/components/ContextMenu/index.vue` 和 `src/composables/useContextMenu.js`

## 实现的功能

### 1. 右键菜单触发
- 在附件列表的每个文件项上添加了 `@contextmenu.prevent="showContextMenu($event, item)"` 事件监听
- 阻止了默认的浏览器右键菜单
- 显示自定义的右键菜单

### 2. 右键菜单内容
- 菜单项：下载附件
- 包含下载图标和文字
- 点击菜单项会调用 `downloadFileDirectly` 函数，直接在当前页面下载文件

### 3. 菜单定位和样式
- 菜单位置根据鼠标点击位置动态计算
- 使用固定定位，确保菜单显示在正确位置
- 添加了美观的样式，包括阴影、圆角、悬停效果

### 4. 菜单管理
- 添加了菜单显示/隐藏的状态管理
- 点击其他地方自动隐藏菜单
- 在组件卸载时清理事件监听器

## 技术实现

### 组件化架构
- **ContextMenu 组件**：`src/components/ContextMenu/index.vue`
- **组合式函数**：`src/composables/useContextMenu.js`
- **使用示例**：参考 `src/components/ContextMenu/README.md`

### 核心方法
- `showContextMenu(event, item)`: 显示右键菜单
- `hideContextMenu()`: 隐藏右键菜单
- `handleDownloadAttachment()`: 处理下载附件
- `downloadFileDirectly(fileItem)`: 直接下载文件（不打开新标签页）

### 下载实现方式
- 右键下载：使用隐藏的 `<a>` 标签直接下载，不打开新标签页
- 原有下载按钮：使用 `window.open()` 打开新标签页
- 两种下载方式都使用相同的权限验证逻辑

### 组件特性
- ✅ 支持自定义菜单项
- ✅ 支持图标显示
- ✅ 自动定位到鼠标位置
- ✅ 点击外部自动隐藏
- ✅ 完全可定制样式
- ✅ 易于在其他页面复用

## 使用方式
1. 在方案详情页面的附件列表中
2. 右键点击任意附件文件
3. 在弹出的菜单中选择"下载附件"
4. 文件会直接在当前页面开始下载，不会打开新标签页

## 注意事项
- 右键下载功能直接在当前页面下载文件，不会打开新标签页
- 原有的下载按钮功能保持不变（仍会打开新标签页）
- 不影响原有的左键点击功能
- 遵循了不改变原有功能的原则
- 代码结构清晰，易于维护和扩展
- **组件已封装，可在其他页面直接复用**

## 在其他页面使用

### 1. 引入组件和组合式函数
```javascript
import ContextMenu from '@/components/ContextMenu/index.vue';
import { useContextMenu, createDownloadMenuItem } from '@/composables/useContextMenu.js';
```

### 2. 使用组合式函数
```javascript
const { 
  contextMenuVisible, 
  contextMenuX, 
  contextMenuY, 
  showContextMenu,
  handleMenuItemClick,
  handleMenuClick
} = useContextMenu([
  createDownloadMenuItem('下载文件', '/path/to/icon.png')
]);
```

### 3. 在模板中使用
```vue
<ContextMenu
  :visible="contextMenuVisible"
  :x="contextMenuX"
  :y="contextMenuY"
  :menuItems="menuItems"
  @item-click="handleMenuItemClick"
  @menu-click="handleMenuClick"
/>
```

详细使用说明请参考：`src/components/ContextMenu/README.md` 